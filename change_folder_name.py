import os
import sys

def rename_folders(path):
    for root, dirs, files in os.walk(path):
        for name in dirs:
            if ' ' in name:
                old_name = os.path.join(root, name)
                new_name = os.path.join(root, name.replace(' ', '_'))
                os.rename(old_name, new_name)
                print(f'Renamed folder: {old_name} -> {new_name}')

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python script.py <directory_path>")
        sys.exit(1)

    directory_path = sys.argv[1]
    rename_folders(directory_path)
