
----------------------------------------
REQUESTS
id, url, body, timestamp, http_method
----------------------------------------

----------------------------------------
SQLITE_SEQUENCE
name, seq
----------------------------------------
logs, 74, 



----------------------------------------
LOGS
id, troubleshooting_id, osVer, log, timestamp
----------------------------------------
1, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), setThreatIntelligenceServerURL: https://1070-tla.cloud.v-key.com, 2025-07-25 11:46:56, 


2, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), ******** This V-OS App Protection [RELEASE] SDK ********(434531c) was built on Jun  7 2025 at 11:36:31 ********, 2025-07-25 11:46:56, 


3, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), ---MAIN LOG START---, 2025-07-25 11:46:56, 


4, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), deviceType: Apple
deviceModel: iPhone14,4
deviceProcessor: N/A
deviceArchitecture: ARM64E
deviceRAM: 3.59
deviceInteralStorageTotalSpace: 119.09
deviceInteralStorageAvailableSpace: 85.28
deviceInteralStorageUsedSpace: 33.80
deviceScreenSize: {375, 812}
deviceScreenResolution: {1080, 2340}, 2025-07-25 11:46:56, 


5, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), Pre v-os: Out_of_band Crypto hook Passed, 2025-07-25 11:46:56, 


6, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), Pre v-os: clear items from Keychain, 2025-07-25 11:46:56, 


7, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), Save __firmware__ to document directory, 2025-07-25 11:46:56, 


8, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), Pre v-os: firmware loaded, 2025-07-25 11:46:56, 


9, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), Using legacy signature file, 2025-07-25 11:46:56, 


10, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), storage signature not found, 2025-07-25 11:46:56, 


11, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), Save __signature__ to document directory, 2025-07-25 11:46:56, 


12, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), Pre v-os: signature loaded, 2025-07-25 11:46:56, 


13, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), Save __profile__ to document directory, 2025-07-25 11:46:56, 


14, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), v-os call back : eventID - 33554432, status - 0, 2025-07-25 11:46:56, 


15, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), License TrustAnchor empty, 2025-07-25 11:46:56, 


16, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), V-OS Processor Version: ********, 2025-07-25 11:46:56, 


17, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), V-OS Firmware Version: ********, 2025-07-25 11:46:56, 


18, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), Post v-os: v-os started (**********), generating troubleshootingID, 2025-07-25 11:46:56, 


19, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), OTA license is empty: (null), 2025-07-25 11:46:56, 


20, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), Profile decrypted, 2025-07-25 11:46:56, 


21, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), Post v-os: Crypto Mode = 1, 2025-07-25 11:46:56, 


22, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), Post v-os: password created, 2025-07-25 11:46:56, 


23, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), signature 4.10.0.9 loaded, 2025-07-25 11:46:56, 


24, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), Post v-os: signature decrypted, 2025-07-25 11:46:56, 


25, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), sendDeviceInfo, 2025-07-25 11:46:56, 


26, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), sendDeviceInfo - request body Dict: {
    "customer_id" = 1070;
    "dd_hash" = 0d4d786935f621970a5e9f457f18e8ead53d1bab2b7d84754023d0342aa97457;
    "device_info" =     {
        architecture = ARM64E;
        "device_id" = 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703;
        "device_location" = "<null>";
        model = "iPhone14,4";
        os = iOS;
        "os_ver" = "18.5";
        type = Apple;
    };
}, 2025-07-25 11:46:56, 


27, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), sendAppInfo, 2025-07-25 11:46:56, 


28, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), sendAppInfo - request body Dict: {
    "ad_hash" = 79b65766e86b8f67930582fa214bb9e725d77420b2e0ca931a31a2519e53ebde;
    "app_info" =     {
        "additional_info" = "{\"profile_id\":\"1043\",\"vos_processor_ver\":\"********\",\"app_ver\":\"1.0.18\",\"profile_ver\":\"3\",\"vos_firmware_ver\":\"********\"}";
        "app_bundle_id" = "com.panin.mypanin";
        "app_signatory_hash" = 6aa53aa4e0b11114035939d9f2e23d0d885af370;
        "vg_ver" = "********";
    };
    "customer_id" = 1070;
    "device_id" = 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703;
}, 2025-07-25 11:46:56, 


29, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), sendMiscInfo, 2025-07-25 11:46:56, 


30, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), TI send Misc info, 2025-07-25 11:46:56, 


31, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), Post v-os: assets loaded, Managebility: 1, 2025-07-25 11:46:56, 


32, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), VGUARD_STATUS : VGUARD_INITIALIZATION_SUCCEEDED, error (null) - (0), 2025-07-25 11:46:56, 


33, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), VGUARD_INITIALIZATION_SUCCEEDED, 2025-07-25 11:46:56, 


34, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), InitializeVGuard succeeded, 2025-07-25 11:46:56, 


35, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), sendMiscInfo - response: <NSHTTPURLResponse: 0x133b78b80> { URL: https://1070-tla.cloud.v-key.com/threat-intel/send-misc-information } { Status Code: 200, Headers {
    "Content-Length" =     (
        13
    );
    "Content-Type" =     (
        "text/plain"
    );
    Date =     (
        "Fri, 25 Jul 2025 04:46:57 GMT"
    );
    "Strict-Transport-Security" =     (
        "max-age=31536000; includeSubDomains"
    );
} } - request: <NSMutableURLRequest: 0x133b786e0> { URL: https://1070-tla.cloud.v-key.com/threat-intel/send-misc-information }, 2025-07-25 11:46:57, 


36, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), sendDeviceInfo - response status code: 200, 2025-07-25 11:46:57, 


37, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), Policy TimeLimit: 0 - Expiry: 0, 2025-07-25 11:46:57, 


38, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), Skip policy check, 2025-07-25 11:46:57, 


39, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), kickstart scan, 2025-07-25 11:46:57, 


40, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), sendAppInfo - response: <NSHTTPURLResponse: 0x133b790e0> { URL: https://1070-tla.cloud.v-key.com/threat-intel/send-app-information } { Status Code: 200, Headers {
    "Cache-Control" =     (
        "no-cache, no-store, max-age=0, must-revalidate"
    );
    "Content-Length" =     (
        0
    );
    Date =     (
        "Fri, 25 Jul 2025 04:46:57 GMT"
    );
    Expires =     (
        0
    );
    Pragma =     (
        "no-cache"
    );
    "Strict-Transport-Security" =     (
        "max-age=31536000; includeSubDomains"
    );
    Vary =     (
        Origin,
        "Access-Control-Request-Method",
        "Access-Control-Request-Headers"
    );
    "x-content-type-options" =     (
        nosniff
    );
    "x-frame-options" =     (
        DENY
    );
    "x-xss-protection" =     (
        0
    );
} } - request: <NSMutableURLRequest: 0x133b78500> { URL: https://1070-tla.cloud.v-key.com/threat-intel/send-app-information }, 2025-07-25 11:46:57, 


41, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), getSignatureWS, 2025-07-25 11:46:57, 


42, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), startScan, 2025-07-25 11:46:57, 


43, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), getProfileWS, 2025-07-25 11:46:57, 


44, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), Perform L1L2 Check, 2025-07-25 11:46:57, 


45, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), GETProfileWS 2: https://cloud.v-key.com/vtrack/webservice/mobile/profile?profileId=1043&profileModified=1752809755&deviceIdentifier=3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703&app=com.panin.mypanin&ver=1.0.18, 2025-07-25 11:46:57, 


46, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), getFirmwareWS, 2025-07-25 11:46:57, 


47, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), GETFirmwareWS 2: https://cloud.v-key.com/vtrack/webservice/mobile/firmware/latest?currentVer=********&companyId=1070&_type=json, 2025-07-25 11:46:57, 


48, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), mount name: /, 2025-07-25 11:46:57, 


49, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), Completed L1L2 check, 2025-07-25 11:46:57, 


50, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), Perform L3 Check, 2025-07-25 11:46:57, 


51, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), undefined signature detected: verify_function[8]: _platform_strcmp in /usr/lib/system/libsystem_platform.dylib, 2025-07-25 11:46:57, 


52, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), check_C_functions, 2025-07-25 11:46:57, 


53, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), Completed L3 check, 2025-07-25 11:46:57, 


54, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), checkBySignatures: 0 -- checkByHookLoadUnloadList: 0 -- checkByFunctKernelPort: 1, 2025-07-25 11:46:57, 


55, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), successfully fetched threat_info with 'synchronised = 1' - count: 0, 2025-07-25 11:46:57, 


56, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), VGuardThreatManager start sync threat_info, 2025-07-25 11:46:57, 


57, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), no threat detected, 2025-07-25 11:46:57, 


58, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), successfully fetched threat_info - count: 0, 2025-07-25 11:46:57, 


59, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), successfully fetched threat_info with 'synchronised = 0' - count: 0, 2025-07-25 11:46:57, 


60, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), Server Version >= 4.8, 2025-07-25 11:46:57, 


61, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), sendScanHeartbeat, 2025-07-25 11:46:57, 


62, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), V-Guard status: 1, 2025-07-25 11:46:57, 


63, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), Scan ended!, 2025-07-25 11:46:57, 


64, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), sendScanHeartbeat - response status code: 200, 2025-07-25 11:46:57, 


65, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), WEB_SERVICE_NAME - 2, Response Status Code: 204, 2025-07-25 11:46:58, 


66, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), response data length <= 0, 2025-07-25 11:46:58, 


67, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), no firmware update, 2025-07-25 11:46:58, 


68, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), WEB_SERVICE_NAME - 0, Response Status Code: 204, 2025-07-25 11:46:58, 


69, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), response data length <= 0, 2025-07-25 11:46:58, 


70, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), no signature update, 2025-07-25 11:46:58, 


71, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), WEB_SERVICE_NAME - 1, Response Status Code: 200, 2025-07-25 11:46:58, 


72, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), Save __profile__ to document directory, 2025-07-25 11:46:58, 


73, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), OTA profile saved successfully, 2025-07-25 11:46:58, 


74, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5/********(434531c), OTA license is empty: (null), 2025-07-25 11:46:58, 



----------------------------------------
ENV
device
----------------------------------------
iPhone14,4 -- Darwin -- 24.5.0 -- Darwin Kernel Version 24.5.0: Tue Apr 22 20:37:55 PDT 2025; root:xnu-11417.122.4~1/RELEASE_ARM64_T8110, 



----------------------------------------
THREAT_INFO
id, hash, threat_class, threat_name, threat_info, threat_pakage, active, remark, synchronised
----------------------------------------
