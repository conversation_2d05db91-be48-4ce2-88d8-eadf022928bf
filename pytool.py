#! /usr/bin/env python
# redme: check log file and highlight error code
# run command: python3 tommyChecklog.py <log file>

import os
import sys
from termcolor import colored
from docx import Document
from docx.shared import RGBColor
import requests
from datetime import datetime
import re
from urllib.parse import urlparse

# Define the color mapping for 'green', 'yellow', and 'blue'
color_map = {
    'green': RGBColor(0, 128, 0),
    'blue': RGBColor(0, 0, 255),
    'red': RGBColor(255, 0, 0),
    'yellow': RGBColor(255, 165, 0)
}

# Define ANSI escape codes for text colors
ANSI_COLORS = {
    'red': "\033[31m",
    'green': "\033[32m",
    'blue': "\033[34m",
    'purple': "\033[35m",
    'cyan': "\033[36m",
    'orange': "\033[38;5;208m",
    'reset': "\033[0m"
}

LOG_FILE = "Result.docx"
ERROR_CODES_FILE = '/Users/<USER>/Documents/Log/filtered_error.txt'

# Global state to be managed within functions
state = {
    'main_count': 0,
    'debugFirmware': 'N/A',
    'debugVOS': 'N/A',
    'debugVguard': 'N/A',
    'debugVtap': 'N/A',
    'firmwareVersion': 0,
    'vosVersion': 0,
    'vguardVersion': 0,
    'vtapVersion': 0
}

def check_conflict_SDK_conflict(log_line):
    debug_dict = {
        'V-OS Processor': 'debugVOS',
        'V-OS Firmware': 'debugFirmware',
        'App Protection': 'debugVguard',
        'V-OS Smart Token': 'debugVtap'
    }
    for key, value in debug_dict.items():
        if key in log_line and 'initVOSWithCompletion' not in log_line:
            state[value] = 'debug' if any(word in log_line for word in ['DEBUG', 'debug', 'Debug']) else 'release'
    outputString = f"\n    VOS:{state['debugVOS']}\n    Vguard:{state['debugVguard']}\n    Firmware:{state['debugFirmware']}\n    Vtap:{state['debugVtap']}\n"
    if not any(status == 'N/A' for status in state.values()):
        Multi_color_wordsTest(f"S##### --> Vkey Component info:{outputString}", {'debug':'red', 'release':'green','S##### --> Vkey Component info:':'yellow', 'N/A':'blue'})

def get_version_from_log(log_line):
    version_pattern = r'Version: (\d+\.\d+\.\d+\.\d+)' if 'V-OS Processor' in log_line else \
                      r'Version: (\d+\.\d+\.\d+\.\d+)' if 'V-OS Firmware' in log_line else \
                      r'SDK (\d+\.\d+\.\d+\.\d+)' if 'App Protection' in log_line else \
                      r'SDK (\d+\.\d+\.\d+\.\d+)'
    match = re.search(version_pattern, log_line)
    if match:
        if 'V-OS Processor' in log_line:
            update_version('vosVersion', match.group(1), 'V-OS Processor')
        elif 'V-OS Firmware' in log_line:
            update_version('firmwareVersion', match.group(1), 'V-OS Firmware')
        elif 'App Protection' in log_line:
            update_version('vguardVersion', match.group(1), 'App Protection')
        elif 'V-OS Smart Token' in log_line:
            update_version('vtapVersion', match.group(1), 'V-OS Smart Token')

def update_version(version_type, new_version, component):
    old_version = state[version_type]
    state[version_type] = new_version
    print(colored(f"- old {component}: {old_version}", "black"))
    print(colored(f"+ new {component}: {new_version}", "blue"))

def color_words(string, words_to_color, color):
    for word in words_to_color:
        string = string.replace(word, colored(word, color))
    print(string)

def Multi_color_wordsTest(string, words_to_color):
    for word, color in words_to_color.items():
        string = string.replace(word, colored(word, color))
    print(string)

def color_word_withBackground(background_color, text_color, string):
    print(background_color + text_color + string + ANSI_COLORS['reset'])

def makeOutputLink():
    print(colored(f"File output :{LOG_FILE}", "magenta"))

def hello():
    big_text = """
 _   _      _ _
| | | |    | | |
| |_| | ___| | | ___
|  _  |/ _ \\ | |/ _ \\
| | | |  __/ | |  __/
\\_| |_/\\___|_|_|\\___|
"""
    print(big_text)

def check_docx_file():
    if not os.path.exists(LOG_FILE):
        Document().save(LOG_FILE)

def download_file_from_url(url, filename):
    try:
        response = requests.get(url)
        if response.status_code == 200:
            with open(filename, "wb") as file:
                file.write(response.content)
            return True
        return False
    except Exception as e:
        print(f"Error downloading file from URL: {e}")
        return False

def log_out_line_number(error_message, line, highlight_word, color, linenum, input_log_file):
    error_color = color if color in color_map else "red"
    print(colored(f"{error_color.upper()}##### --> {error_message}:", error_color))
    output_string = f"{input_log_file}:{linenum}  |   {line}"
    color_words(output_string, highlight_word, color)
    save_to_docx(error_message, line, color)

def save_to_docx(error_message, line, color):
    check_docx_file()
    document = Document(LOG_FILE)
    p = document.add_paragraph('')
    run = p.add_run(f"{error_message}")
    run.bold = True
    run.font.color.rgb = color_map.get(color, color_map["red"])
    document.add_paragraph(line)
    document.save(LOG_FILE)

def load_error_codes():
    try:
        with open(ERROR_CODES_FILE, "r", encoding='utf-8', errors='ignore') as file:
            return dict(line.strip().split() for line in file)
    except FileNotFoundError:
        print(f"Error: File '{ERROR_CODES_FILE}' not found.")
        return {}

def extract_filename_from_url(url):
    parsed_url = urlparse(url)
    return os.path.basename(parsed_url.path)

def main():
    error_fromLog = load_error_codes()
    argument_count = len(sys.argv) - 1
    print("Number of file input: ", argument_count)
    for i in range(1, len(sys.argv)):
        hello()
        state.update(main_count=0, debugFirmware='N/A', debugVOS='N/A', debugVguard='N/A', debugVtap='N/A',
                     firmwareVersion=0, vosVersion=0, vguardVersion=0, vtapVersion=0)
        inputLogFile = sys.argv[i]
        check_docx_file()
        document = Document(LOG_FILE)
        document.add_heading(datetime.now().strftime("%Y-%m-%d %H:%M:%S"), level=3)
        document.add_heading(f"[[File]] : {inputLogFile}", level=1)
        document.save(LOG_FILE)
        if inputLogFile.startswith("http://") or inputLogFile.startswith("https://"):
            filename = extract_filename_from_url(inputLogFile)
            if not download_file_from_url(inputLogFile, filename):
                print(f"Failed to download the file from URL: {inputLogFile}")
                continue
            inputLogFile = filename
        print(colored(f"File input: {inputLogFile}", "magenta"))
        with open(inputLogFile, 'r', encoding='utf-8', errors='ignore') as file:
            for line_num, line in enumerate(file, start=1):
                check_log_line(line, line_num, inputLogFile, error_fromLog)

def check_log_line(line, line_num, input_log_file, error_fromLog):
    if 'V-OS Firmware' in line:
        handle_log_line("SDK Firmware Version", line, ["V-OS Firmware","[DEBUG]","PQR","Debug","Release"], "yellow", line_num, input_log_file, error_fromLog)
    elif "Server Version >= 4.8" in line:
        handle_log_line("Server Version >= 4.8", line, ["Server Version >= 4.8"], "yellow", line_num, input_log_file, error_fromLog)
    elif "Version lower than 4.8" in line:
        handle_log_line("Version lower than 4.8", line, ["SVersion lower than 4.8"], "yellow", line_num, input_log_file, error_fromLog)
    for error, color in error_fromLog.items():
        if error in line:
            handle_log_line("Error found", line, [error], color, line_num, input_log_file, error_fromLog)

def handle_log_line(message, line, highlight_words, color, line_num, input_log_file, error_fromLog):
    log_out_line_number(message, line, highlight_words, color, line_num, input_log_file)
    get_version_from_log(line)
    check_conflict_SDK_conflict(line)

if __name__ == "__main__":
    main()
