import requests
import pyzipper
import os
import re

TEMPLATE_PATH = "template.txt"
download_dir = "./downloads"
unzip_dir = "./unzipped"

# === Helper to parse key-value lines ===
def parse_template(filepath):
    config = {}
    with open(filepath, "r", encoding="utf-8") as file:
        for line in file:
            match = re.match(r"^(.*?):\s*(.*)$", line.strip())
            if match:
                key = match.group(1).strip().lower().replace(" ", "_")
                value = match.group(2).strip()
                config[key] = value
    return config

# === Load config from template ===
config = parse_template(TEMPLATE_PATH)

url = config.get("url")
username = config.get("username")
password = config.get("password")
unzip_password = config.get("unzip_password")
zip_filename = url.split("/")[-1]

# === Ensure folders exist ===
os.makedirs(download_dir, exist_ok=True)
os.makedirs(unzip_dir, exist_ok=True)

# === Session for HTTP basic auth download ===
with requests.Session() as session:
    response = session.get(url, auth=(username, password), stream=True)

    if response.status_code == 200:
        zip_path = os.path.join(download_dir, zip_filename)
        print(f"Downloading to {zip_path}...")

        with open(zip_path, "wb") as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)

        print("Download complete. Extracting...")

        # === Unzip with password ===
        with pyzipper.AESZipFile(zip_path) as zf:
            zf.pwd = unzip_password.encode()
            zf.extractall(path=unzip_dir)

            # List of all files/folders in the zip
            zip_file_list = zf.namelist()

        # === Smart change into inner folder if exists ===
        top_level_items = {item.split('/')[0] for item in zip_file_list if '/' in item}
        if len(top_level_items) == 1:
            inner_folder = os.path.join(unzip_dir, list(top_level_items)[0])
            if os.path.isdir(inner_folder):
                os.chdir(inner_folder)
                print(f"Changed working directory to inner folder: {os.getcwd()}")
            else:
                os.chdir(unzip_dir)
                print(f"Expected folder not found. Staying in unzip dir: {os.getcwd()}")
        else:
            os.chdir(unzip_dir)
            print(f"Changed working directory to unzip dir: {os.getcwd()}")

    else:
        print(f"Download failed. Status code: {response.status_code}")