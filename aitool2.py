#! /usr/bin/env python
# Script to check log file and highlight error codes
# Usage: python checkLogCSTool.py <log file>

import os
import sys
import subprocess
import platform
import argparse

# Clear the command line based on the OS
def clear_console():
    if platform.system() == "Windows":
        os.system("cls")
    else:
        os.system("clear")

clear_console()

# Function to install missing dependencies
def install_package(package):
    try:
        __import__(package)
    except ImportError:
        subprocess.run([sys.executable, "-m", "pip", "install", package], check=True)

# Ensure required packages are installed
install_package("termcolor")
install_package("python-docx")

from termcolor import colored
from docx import Document

# Argument parser for command-line handling
parser = argparse.ArgumentParser(description="Check log file and highlight error codes.")
parser.add_argument("logfile", type=str, help="Path to the log file")

args = parser.parse_args()

# Verify log file exists
if not os.path.exists(args.logfile):
    print(colored(f"Error: File '{args.logfile}' not found.", "red"))
    sys.exit(1)

# Function to process log file
def process_log_file(logfile):
    with open(logfile, "r", encoding="utf-8") as file:
        lines = file.readlines()

    document = Document()
    for line in lines:
        if "ERROR" in line:
            print(colored(line.strip(), "red"))
            document.add_paragraph(line.strip(), style="Heading 1")
        else:
            print(line.strip())
            document.add_paragraph(line.strip())

    document.save("LogReport.docx")
    print(colored("Log file processing completed. Report saved as 'LogReport.docx'", "green"))

# Process the log file
process_log_file(args.logfile)
