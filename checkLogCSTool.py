#! /usr/bin/env python
# redme: check log file and highlight error code
# run command: python checkLogCSTool.py <log file>

import os
import sys
import subprocess

# Clear the terminal
os.system('clear')
os.system('clear')
print(f"[INFO] Running tool: {os.path.abspath(__file__)}")
# os.system('pip3 install termcolor requests re python-docx')
# pip3 install termcolor requests re python-docx --break-system-packages
from termcolor import colored
# try:
#     from termcolor import colored
# except ImportError:
#     subprocess.check_call([sys.executable, "-m", "pip3", "install", "termcolor"])
#     from termcolor import colored
# for color a list of word
# write log to word file
from docx import Document
# try:
#     from docx import Document
# except ImportError:
#     subprocess.check_call([sys.executable, "-m", "pip3", "install", "docx"])
#     from termcolor import colored
from docx.shared import RGBColor
import requests
from datetime import datetime
import re # check update case
from urllib.parse import urlparse

# Define the color mapping for 'green', 'yellow', and 'blue'
color_map = {
    'green': RGBColor(0, 128, 0),  # Dark green
    # 'yellow': RGBColor(255, 255, 0),  # Yellow
    'blue': RGBColor(0, 0, 255),  # Blue
	'red': RGBColor(255, 0, 0),  # Red
    'yellow': RGBColor(255, 165, 0)  # Orange
}
# Global variable declaration
main_count = 0
ERROR_CODES_FILE = '/Users/<USER>/Documents/Log/filtered_error.txt'

inputLogFile = ""
error_fromLog = dict()
firmwareVersion = 0
vosVersion = 0
vguardVersion = 0
vtapVersion = 0
# variable to check conflict Debug, Release SDK
debugFirmware = 'N/A'
debugVOS = 'N/A'
debugVguard = 'N/A'
debugVtap = 'N/A'

# Define ANSI escape codes for text colors and formatting
RED_TEXT = "\033[31m"
GREEN_TEXT = "\033[32m"
YELLOW_TEXT = "\033[33m"
BLUE_TEXT = "\033[34m"
MAGENTA_TEXT = "\033[35m"
CYAN_TEXT = "\033[36m"
WHITE_TEXT = "\033[37m"
BRIGHT_RED = "\033[91m"
BRIGHT_GREEN = "\033[92m"
BRIGHT_YELLOW = "\033[93m"
BRIGHT_BLUE = "\033[94m"
BRIGHT_MAGENTA = "\033[95m"
BRIGHT_CYAN = "\033[96m"
BRIGHT_WHITE = "\033[97m"
BLACK_TEXT = "\033[30m"

# Background colors
RED_BG = "\033[41m"
GREEN_BG = "\033[42m"
YELLOW_BG = "\033[43m"
BLUE_BG = "\033[44m"
MAGENTA_BG = "\033[45m"
CYAN_BG = "\033[46m"
WHITE_BG = "\033[47m"

# Text formatting
BOLD = "\033[1m"
DIM = "\033[2m"
UNDERLINE = "\033[4m"
BLINK = "\033[5m"
REVERSE = "\033[7m"
RESET = "\033[0m"  # Reset text formatting

# Box drawing characters for better visual separation
BOX_HORIZONTAL = "─"
BOX_VERTICAL = "│"
BOX_TOP_LEFT = "┌"
BOX_TOP_RIGHT = "┐"
BOX_BOTTOM_LEFT = "└"
BOX_BOTTOM_RIGHT = "┘"
BOX_CROSS = "┼"
BOX_T_DOWN = "┬"
BOX_T_UP = "┴"
BOX_T_RIGHT = "├"
BOX_T_LEFT = "┤"

def print_separator(char="─", length=80, color=""):
    """Print a separator line for better visual separation"""
    separator = char * length
    if color:
        print(f"{color}{separator}{RESET}")
    else:
        print(separator)

def print_section_header(title, color=BRIGHT_CYAN):
    """Print a section header with box drawing"""
    title_len = len(title)
    box_width = max(60, title_len + 10)
    padding = (box_width - title_len - 2) // 2

    print(f"\n{color}{BOX_TOP_LEFT}{BOX_HORIZONTAL * (box_width - 2)}{BOX_TOP_RIGHT}")
    print(f"{BOX_VERTICAL}{' ' * padding}{BOLD}{title}{RESET}{color}{' ' * (box_width - title_len - padding - 2)}{BOX_VERTICAL}")
    print(f"{BOX_BOTTOM_LEFT}{BOX_HORIZONTAL * (box_width - 2)}{BOX_BOTTOM_RIGHT}{RESET}\n")

def print_status_badge(status, message=""):
    """Print a colored status badge"""
    badges = {
        "SUCCESS": f"{GREEN_BG}{WHITE_TEXT} ✓ SUCCESS {RESET}",
        "ERROR": f"{RED_BG}{WHITE_TEXT} ✗ ERROR {RESET}",
        "WARNING": f"{YELLOW_BG}{BLACK_TEXT} ⚠ WARNING {RESET}",
        "INFO": f"{BLUE_BG}{WHITE_TEXT} ℹ INFO {RESET}",
        "DEBUG": f"{MAGENTA_BG}{WHITE_TEXT} ⚙ DEBUG {RESET}"
    }
    badge = badges.get(status.upper(), f"{WHITE_BG}{BLACK_TEXT} {status.upper()} {RESET}")
    if message:
        print(f"{badge} {message}")
    else:
        print(badge)

def hello():
    big_text = f"""{BRIGHT_CYAN}
 _   _      _ _
| | | |    | | |
| |_| | ___| | | ___
|  _  |/ _ \ | |/ _ \\
| | | |  __/ | |  __/
\_| |_/\___|_|_|\___|
{BRIGHT_YELLOW}Version 2.0{RESET}
"""
    print(big_text)
    print_separator("═", 60, BRIGHT_CYAN)

# This function checks for conflicts in the SDK based on the log line provided.
def check_conflict_SDK_conflict(log_line):
    # Declare global variables that store the debug status of different components.
    global debugFirmware, debugVOS, debugVguard, debugVtap

    # Create a dictionary to map the log line keys to the corresponding global variables.
    debug_dict = {# key, value
        'V-OS Processor': 'debugVOS',
        'V-OS Firmware': 'debugFirmware',
        'App Protection': 'debugVguard',
        'V-OS Smart Token': 'debugVtap'
    }

    # Iterate over each key-value pair in the dictionary.
    for key, value in debug_dict.items():
        # If the key is found in the log line and 'initVOSWithCompletion' is not in the log line,
        # check if any form of the word 'debug' is in the log line.
        if key in log_line and 'initVOSWithCompletion' not in log_line:
            # If 'debug' is found, set the corresponding global variable to 'debug'.
            # Otherwise, set it to 'release'.
            globals()[value] = 'debug' if 'DEBUG' in log_line or 'debug' in log_line or 'Debug' in log_line else 'release'

    # Show component info box whenever any component is detected/updated
    global vosVersion, vguardVersion, firmwareVersion, vtapVersion

    # Format each component with its version if available
    vos_info = f"{debugVOS} ({vosVersion})" if vosVersion != 0 else debugVOS
    vguard_info = f"{debugVguard} ({vguardVersion})" if vguardVersion != 0 else debugVguard
    firmware_info = f"{debugFirmware} ({firmwareVersion})" if firmwareVersion != 0 else debugFirmware
    vtap_info = f"{debugVtap} ({vtapVersion})" if vtapVersion != 0 else debugVtap

    # Calculate the maximum width needed for proper alignment
    max_width = 60

    # Create a styled component info box
    print(f"\n{BRIGHT_YELLOW}{BOX_TOP_LEFT}{BOX_HORIZONTAL * (max_width - 2)}{BOX_TOP_RIGHT}")
    print(f"{BOX_VERTICAL}{BOLD} Vkey Component Info{RESET}{BRIGHT_YELLOW}{' ' * (max_width - 22)}{BOX_VERTICAL}")
    print(f"{BOX_T_RIGHT}{BOX_HORIZONTAL * (max_width - 2)}{BOX_T_LEFT}")

    # Print each component with proper styling and fixed width
    def format_component_line(label, info, debug_status):
        color = GREEN_TEXT if debug_status == 'release' else RED_TEXT if debug_status != 'N/A' else DIM
        padding = max_width - len(label) - len(info) - 4
        return f"{BOX_VERTICAL} {label}: {color}{info}{RESET}{BRIGHT_YELLOW}{' ' * padding}{BOX_VERTICAL}"

    print(format_component_line("VOS     ", vos_info, debugVOS))
    print(format_component_line("Vguard  ", vguard_info, debugVguard))
    print(format_component_line("Firmware", firmware_info, debugFirmware))
    print(format_component_line("Vtap    ", vtap_info, debugVtap))

    # Add the triggering log line for reference
    if log_line and log_line.strip():
        print(f"{BOX_T_RIGHT}{BOX_HORIZONTAL * (max_width - 2)}{BOX_T_LEFT}")
        print(f"{BOX_VERTICAL}{DIM} Triggered by:{RESET}{BRIGHT_YELLOW}{' ' * (max_width - 16)}{BOX_VERTICAL}")

        # Truncate long log lines to fit in the box
        log_display = log_line.strip()
        if len(log_display) > max_width - 6:
            log_display = log_display[:max_width - 9] + "..."

        log_padding = max_width - len(log_display) - 4
        print(f"{BOX_VERTICAL} {DIM}{log_display}{RESET}{BRIGHT_YELLOW}{' ' * log_padding}{BOX_VERTICAL}")

    print(f"{BOX_BOTTOM_LEFT}{BOX_HORIZONTAL * (max_width - 2)}{BOX_BOTTOM_RIGHT}{RESET}")

    # Check for mixed debug/release components and show warning/success message
    # Only check if we have at least 2 components detected
    detected_components = [comp for comp in [debugVOS, debugVguard, debugFirmware, debugVtap] if comp != 'N/A']
    if len(detected_components) >= 2:
        has_mixed = len(set(detected_components)) > 1  # More than one unique value means mixed
        if has_mixed:
            print(f"{RED_BG}{WHITE_TEXT} ⚠ WARNING: Mixed debug/release components detected! {RESET}")
        else:
            print(f"{GREEN_BG}{WHITE_TEXT} ✓ All detected components are using the same build type {RESET}")
        # log_out_line_number("Vkey Component info", outputString, ["debug","release"], "red" if 'debug' in outputString and 'release' in outputString else "blue", "", "")

# def check_conflict_SDK_conflict(log_line):
#     global debugFirmware, debugVOS, debugVguard, debugVtap
#     # Update debug values based on log_line
#     if 'V-OS Processor' in log_line and  'initVOSWithCompletion' not in log_line:
#         debugVOS = 'debug' if 'DEBUG' in log_line or 'debug' in log_line or 'Debug' in log_line else 'release'
#     elif 'V-OS Firmware' in log_line and 'initVOSWithCompletion' not in log_line:
#         debugFirmware = 'debug' if 'DEBUG' in log_line or 'debug' in log_line or 'Debug' in log_line else 'release'
#     elif 'App Protection' in log_line:
#         debugVguard = 'debug' if 'DEBUG' in log_line or 'debug' in log_line else 'release'
#     elif 'V-OS Smart Token' in log_line:
#         debugVtap = 'debug' if 'DEBUG' in log_line or 'debug' in log_line else 'release'

# 	# print out result:
#     outputString = f"\nVOS:{debugVOS}\nVguard:{debugVguard}\nVtap:{debugVtap}\nFirmware:{debugFirmware}"
#     if debugVOS != 'N/A' and debugVguard != 'N/A' and debugFirmware != 'N/A':
#        log_out_line_number("Vkey Component info", outputString, ["debug","release"], "red" if 'debug' in outputString and 'release' in outputString else "blue", "", "")

def get_version_from_log(log_line):
    if 'V-OS Processor' in log_line:
        version_pattern = r'Version: (\d+\.\d+\.\d+\.\d+)'
    elif 'V-OS Firmware' in log_line:
        version_pattern = r'Version: (\d+\.\d+\.\d+\.\d+)'
    elif 'App Protection' in log_line:
        version_pattern = r'SDK (\d+\.\d+\.\d+\.\d+)'
    elif 'V-OS Smart Token' in log_line:
        version_pattern = r'SDK (\d+\.\d+\.\d+\.\d+)'
    
    match = re.search(version_pattern, log_line)
    if match:
		# this one for make sure u want to change global value
        global firmwareVersion, vosVersion, vguardVersion, vtapVersion

        if 'V-OS Processor' in log_line:
            color_word_withBackground(YELLOW_BG, RED_TEXT, colored(f"- old vos processor: {vosVersion}", "black"))
            vosVersion = match.group(1) if match.group(1) != None else 0
            color_word_withBackground(YELLOW_BG, RED_TEXT, colored(f"+ new vos processor: {vosVersion}", "green"))
        elif 'V-OS Firmware' in log_line:
            color_word_withBackground(YELLOW_BG, RED_TEXT, colored(f"- old firmware: {firmwareVersion}", "black"))
            firmwareVersion = match.group(1) if match.group(1) != None else 0
            color_word_withBackground(YELLOW_BG, RED_TEXT, colored(f"+ new firmware: {firmwareVersion}", "green"))
        elif 'App Protection' in log_line:
            color_word_withBackground(YELLOW_BG, RED_TEXT, colored(f"- old vguard protection: {vguardVersion}", "black"))
            vguardVersion = match.group(1) if match.group(1) != None else 0
            color_word_withBackground(YELLOW_BG, RED_TEXT, colored(f"+ new vguard protection: {vguardVersion}", "green"))
        elif 'V-OS Smart Token' in log_line:
            color_word_withBackground(YELLOW_BG, RED_TEXT, colored(f"- old vtap: {vtapVersion}", "black"))
            vtapVersion = match.group(1) if match.group(1) != None else 0
            color_word_withBackground(YELLOW_BG, RED_TEXT, colored(f"+ new vtap: {vtapVersion}", "green"))

def color_words(string, words_to_color, color):
    colored_string = string
    for word in words_to_color:
        colored_string = colored_string.replace(word, colored(word, color))
    print(colored_string)

def color_wordsTest(string, words_to_color):
    colored_string = string
    for word, color in words_to_color.items():
        colored_string = colored_string.replace(word, colored(word, color))
    print(colored_string)

def color_words_enhanced(string, words_to_color, base_color):
    """Enhanced color function with better highlighting and readability"""
    colored_string = string

    # Apply base color to the entire string first
    if base_color == "red":
        colored_string = f"{DIM}{colored_string}{RESET}"
    elif base_color == "green":
        colored_string = f"{DIM}{colored_string}{RESET}"
    elif base_color == "yellow":
        colored_string = f"{DIM}{colored_string}{RESET}"
    elif base_color == "blue":
        colored_string = f"{DIM}{colored_string}{RESET}"

    # Highlight specific words with bright colors and background
    for word in words_to_color:
        if word in colored_string:
            if base_color == "red":
                highlighted = f"{BRIGHT_RED}{BOLD}{word}{RESET}{DIM}"
            elif base_color == "green":
                highlighted = f"{BRIGHT_GREEN}{BOLD}{word}{RESET}{DIM}"
            elif base_color == "yellow":
                highlighted = f"{BRIGHT_YELLOW}{BOLD}{word}{RESET}{DIM}"
            elif base_color == "blue":
                highlighted = f"{BRIGHT_BLUE}{BOLD}{word}{RESET}{DIM}"
            else:
                highlighted = f"{BRIGHT_WHITE}{BOLD}{word}{RESET}{DIM}"

            colored_string = colored_string.replace(word, highlighted)

    print(colored_string + RESET)

def color_word_withBackground(background_color, text_color, string):
    # Apply background and text color to the entire string
    print(background_color + text_color + f"{string}" + RESET)
    

# Add a new function to download a file from a URL
def download_file_from_url(url, filename):
    try:
        response = requests.get(url)
        if response.status_code == 200:
            with open(filename, "wb") as file:
                file.write(response.content)
            return True
        else:
            return False
    except Exception as e:
        print(f"Error downloading file from URL: {e}")
        return False

def log_out_line_number(error_message, line, highlight_word, color, linenum, inputLogFile):
    """Enhanced log output with better formatting and visual separation"""

    # Color mapping for better readability
    color_mapping = {
        "red": {"bg": RED_BG, "text": WHITE_TEXT, "icon": "✗", "label": "ERROR"},
        "green": {"bg": GREEN_BG, "text": WHITE_TEXT, "icon": "✓", "label": "SUCCESS"},
        "yellow": {"bg": YELLOW_BG, "text": BLACK_TEXT, "icon": "⚠", "label": "WARNING"},
        "blue": {"bg": BLUE_BG, "text": WHITE_TEXT, "icon": "ℹ", "label": "INFO"},
        "magenta": {"bg": MAGENTA_BG, "text": WHITE_TEXT, "icon": "⚙", "label": "DEBUG"},
        "cyan": {"bg": CYAN_BG, "text": WHITE_TEXT, "icon": "📋", "label": "LOG"}
    }

    # Get color scheme or default
    scheme = color_mapping.get(color, {"bg": WHITE_BG, "text": BLACK_TEXT, "icon": "•", "label": "LOG"})

    # Print section separator for better visual grouping
    print(f"\n{DIM}{BOX_HORIZONTAL * 80}{RESET}")

    # Print status badge with icon
    badge = f"{scheme['bg']}{scheme['text']} {scheme['icon']} {scheme['label']} {RESET}"
    print(f"{badge} {BOLD}{error_message}{RESET}")

    # Print file location with better formatting
    location = f"{CYAN_TEXT}📁 {inputLogFile}:{linenum}{RESET}"
    print(f"   {location}")

    # Print the log line with syntax highlighting
    if line.strip():  # Only print if line has content
        print(f"   {DIM}{BOX_VERTICAL}{RESET} ", end="")
        color_words_enhanced(line.strip(), highlight_word, color)

    print()  # Add spacing after each log entry
    
def load_error_codes():
    # Assuming ERROR_CODES_FILE is a valid file path
    try:
        with open(ERROR_CODES_FILE, "r") as file:
            return dict(line.strip().split() for line in file)
    except FileNotFoundError:
        print(f"Error: File '{ERROR_CODES_FILE}' not found.")
        return {}  # Return an empty dictionary or handle the error as needed

def openLogFile(name):
	FileToOpen = f'code {name}'
	print(FileToOpen)
	os.system(FileToOpen) # clear the command line

def extract_filename_from_url(url):
    parsed_url = urlparse(url)
    filename = os.path.basename(parsed_url.path)
    return filename

def main():
	error_fromLog = load_error_codes() # load error list from storage
	argument_count = len(sys.argv)

	# Print welcome header
	hello()
	print_section_header("LOG ANALYSIS TOOL", BRIGHT_CYAN)
	print(f"{BRIGHT_YELLOW}📊 Number of files to process: {argument_count-1}{RESET}\n")

	for i in range(1, len(sys.argv)):
		# Print file processing header
		print_separator("═", 80, BRIGHT_MAGENTA)
		print_section_header(f"PROCESSING FILE {i}/{argument_count-1}", BRIGHT_MAGENTA)

		# reset main count
		global main_count
		main_count = 0
		# reset debug check
		debugFirmware = 'N/A'
		debugVguard = 'N/A'
		debugVOS = 'N/A'
		debugVtap = 'N/A'

		# reset version compare
		firmwareVersion = 0
		vosVersion = 0
		vguardVersion = 0
		vtapVersion = 0

		inputLogFile = sys.argv[i]

		if inputLogFile.startswith("http://") or inputLogFile.startswith("https://"):
			print(f"{BRIGHT_CYAN}🌐 Downloading file from URL...{RESET}")
			filename = extract_filename_from_url(inputLogFile)
			if download_file_from_url(inputLogFile, filename):
				inputLogFile = filename
				print_status_badge("SUCCESS", f"Downloaded: {filename}")
			else:
				print_status_badge("ERROR", f"Failed to download: {inputLogFile}")
				continue

		# Display current file being processed
		print(f"{BRIGHT_CYAN}📁 Processing file: {BOLD}{inputLogFile}{RESET}")
		print_separator("─", 60, DIM)

		# openLogFile(inputLogFile) # open log file to check
		with open(inputLogFile, 'r') as file:
			for line_num, line in enumerate(file, start=1):
				# check V-OS Firmware Version
				if 'V-OS Firmware' in line:
					get_version_from_log(line)
					log_out_line_number("SDK Firmware Version", line, ["V-OS Firmware","[DEBUG]","PQR","Debug","Release"], "yellow", line_num, inputLogFile)
					check_conflict_SDK_conflict(line)

				# check Existing signature
				# elif "signature version" in line:
				# 	log_out_line_number("signature version", line, ["signature", "version"], "yellow", line_num, inputLogFile)

				#check server
				elif "Server Version >= 4.8" in line:
					log_out_line_number("Server Version >= 4.8", line, ["Server Version >= 4.8" ], "yellow", line_num, inputLogFile)
				elif "updateCheckInterval" in line:
					log_out_line_number("updateCheckInterval", line, ["updateCheckInterval" ], "yellow", line_num, inputLogFile)
				# erver Version lower than 4.8
				elif "Version lower than 4.8" in line:
					log_out_line_number("Version lower than 4.8", line, ["SVersion lower than 4.8" ], "yellow", line_num, inputLogFile)
				# check VOS version
				elif 'V-OS Processor' in line:
					get_version_from_log(line)
					log_out_line_number("SDK VOS Version", line, ["V-OS Processor", "[RELEASE]", "[DEBUG]", "Debug", "V-OS Processor", "Release"], "yellow", line_num, inputLogFile)
					check_conflict_SDK_conflict(line)
				# check VGuard version
				elif 'V-OS App Protection' in line:
					get_version_from_log(line)
					log_out_line_number("SDK App Protection version", line, ["V-OS App Protection", "[RELEASE]","[DEBUG]"], "yellow", line_num, inputLogFile)
					check_conflict_SDK_conflict(line)
				# check V-OS Smart Token
				elif 'V-OS Smart Token' in line:
					get_version_from_log(line)
					if vtapVersion != 0:
						log_out_line_number("SDK Smart Token version", line, ["V-OS Smart Token","[DEBUG]"], "yellow", line_num, inputLogFile)
					check_conflict_SDK_conflict(line)
				# check customerID 'customerID'
				elif 'customerID' in line:
					# Try to extract customer ID using regex
					match = re.search(r'customerID[\s:=]+(\d+)', line)
					if match:
						# ANSI escape code for bold: \033[1m ... \033[0m
						print("\033[1m" + colored(f"Extracted customerID: {match.group(1)}", "cyan") + "\033[0m")
					log_out_line_number("customerID", line, ["customerID", "startVOS", ], "yellow", line_num, inputLogFile)
				# get tid from consoleLog:logger_set_tid
				elif 'logger_set_tid():' in line:
					# Try to extract the troubleshooting id (hex string after colon)
					match = re.search(r'logger_set_tid\(\):\s*([0-9a-fA-F]+)', line)
					if match:
						print("\033[1m" + colored(f"Extracted troubleshooting ID: {match.group(1)}", "cyan") + "\033[0m")
					log_out_line_number("VOS -->Console log --> logger_set_tid:", line, ["logger_set_tid():"], "yellow", line_num, inputLogFile)
				#crypto mode
				elif 'Crypto Mode' in line:
					log_out_line_number("Crypto Mode = 1 | pqr | Strong" if 'Crypto Mode = 1' in line else "Crypto Mode = 0 | nonpqr | Light", line, ["Crypto Mode"], "yellow", line_num, inputLogFile)
					# 2023-11-01 10:10:23.527516+0700 Test[15958:567031] -[VGuardManager initVOSWithCompletion:]_block_invoke [Line 801] Post v-os: Crypto Mode = 1
				#asset managed mode
				elif 'Managebility:' in line:
					assetMode = "Asset Managebility Mode = 1" if "Managebility: 1" in line else "Asset Managebility Mode = 0"
					log_out_line_number(assetMode, line, ["Managebility: 0","Managebility: 1"], "yellow", line_num, inputLogFile)
					#22, 16843E95671F30DDB47067C7006A431CA82B37F4, 15.7.9/4.9.1.12(433540d), Post v-os: assets loaded, Managebility: 1, 2023-11-01 11:26:36, 

				# main log
				elif 'MAIN LOG START---' in line:
					main_count += 1
					log_out_line_number(f"---MAIN LOG START: {main_count} ------------------------------------------------", "", ["---MAIN LOG START---"], "blue", line_num, inputLogFile)
				
				# check ALLPROVISIONEDTOKEN_KEY is nil
				elif line.find('ALLPROVISIONEDTOKEN_KEY is nil') != -1:
					log_out_line_number("ALLPROVISIONEDTOKEN_KEY is nil", line, ["ALLPROVISIONEDTOKEN_KEY is nil"], "yellow", line_num, inputLogFile)

				# check androi log
				elif 'com.google.android' in line and main_count == 0:
					log_out_line_number('ANDROID log', line, ['com.google.android'], "yellow", line_num, inputLogFile)

				# check firmware file
				elif 'The file "firmware" could' in line:
					log_out_line_number('Missing "firmware"', line, ['"firmware"','no such file','/firmware','No such file or directory'], "red", line_num, inputLogFile)

				elif 'Profile decryption failed' in line:
					log_out_line_number('Profile decryption failed"', line, ["Profile decryption failed"], "red", line_num, inputLogFile)

				elif 'VGuardManager start ...' in line:
					log_out_line_number('start Vguard', line, ["VGuardManager start ..."], "green", line_num, inputLogFile)
				#default	MoMoPlatform Staging	VGuardManager start ...

				# check vos status
				elif 'statusVOS' in line:
					status = 'VOS_NOTOK' if 'VOSSTATUS: 0' in line or 'statusVOS: 0' in line or 'status:0' in line else 'VOS_OK'
					log_out_line_number(status , line, [f"{status}", "VOSSTATUS", "statusVOS"], "red", line_num, inputLogFile) if status == 'VOS_NOTOK' else log_out_line_number(status , line, [f"{status}", "VOSSTATUS", "statusVOS"], "green", line_num, inputLogFile)
					#2023-07-17 11:31:17.805481+0700 DBS_test[1056:5198914] -[VTapManager statusVOS:withError:] [Line 250] VOSSTATUS: 1
				elif "statusVOS" in line and "VOS_OK" in line :
					log_out_line_number("VOS_OK", line, ["VOS_OK"], "green", line_num, inputLogFile)
				#default	15:25:49.108006+0700	momo012	^statusVOS: VOS_OK
				elif '[VGuardManager initVOSWithCompletion:]_block_invoke' in line and 'VOS init successfully' in line :
					log_out_line_number("VOS init successfully", "", ["VOS init successfully"], "green", line_num, inputLogFile)
				# case vos init fail 'init vos fail'
				elif 'init vos fail' in line:
					log_out_line_number("VOS init fail", line, ["init vos fail","[VGuardManager initVOSWithCompletion"], "red", line_num, inputLogFile)
				#check [VGuardManager initializeVGuard]
				elif "[VGuardManager initializeVGuard]" in line and "initiate VguardManager" in line:
					log_out_line_number("start VGuardManager initializeVGuard", line, ["VGuardManager initializeVGuard"], "green", line_num, inputLogFile)
				# check VGUARD_STATUS
				elif 'VGUARD_STATUS - 0' in line or 'V-Guard status: 0' in line or 'VGUARD_UNSAFE' in line: # unsafe case
					log_out_line_number("VGUARD_STATUS: 0 - unsafe", line, ["VGUARD_STATUS - 0","V-Guard status: 0","VGUARD_UNSAFE"], "red", line_num, inputLogFile)

				elif 'statusVGuard() status:1' in line or 'V-Guard status: 1' in line: # success case
					log_out_line_number("VGUARD_STATUS: 1 - safe", line, ["statusVGuard() status:1", "V-Guard status: 1"], "green", line_num, inputLogFile)

				elif "V-Guard status: 2" in line:
					log_out_line_number("VGUARD_UNDETERMINE -2 ", line, ["V-Guard status: 2"], "red", line_num, inputLogFile)
	
				#default	15:17:13.894314+0700	momo012	^statusVGuard: VGUARD_UNSAFE: VGUARD_STATUS(rawValue: 0) :error nil
				elif 'VGUARD_INITIALIZATION_SUCCEEDED' in line: # success case
					log_out_line_number("VGUARD_INITIALIZATION_SUCCEEDED", line, ["VGUARD_INITIALIZATION_SUCCEEDED"], "green", line_num, inputLogFile)

				elif 'VGUARD_INITIALIZATION_FAILED' in line: # fail case
					log_out_line_number("VGUARD_INITIALIZATION_FAILED", line, ["VGUARD_INITIALIZATION_FAILED"], "red", line_num, inputLogFile)
					# vguardLog: 6104	13BD0674AD9642B2F9163888D8038223ED10D886	16.3.1/*******(91c6dcc)	VGUARD_STATUS : VGUARD_INITIALIZATION_FAILED, error VOS failed to read from trusted storage - (-8) - (20035)	2023-07-08 02:41:22
				elif 'vGuardDidFinishInitializing' in line and 'status:' in line: # fail case
					if 'status: 0' in line:
						log_out_line_number("VGUARD_INITIALIZATION_FAILED", line, [ "VTapDatabaseManager", "vGuardDidFinishInitializing", "status: 0"], "red", line_num, inputLogFile)
					if 'status: 1' in line:
						log_out_line_number("VGUARD_INITIALIZATION_SUCCEEDED", line, ["VTapDatabaseManager", "vGuardDidFinishInitializing", "status: 1"], "green", line_num, inputLogFile)
					# vtapLog: 2023-07-17 11:31:17.055347+0700 DBS_test[1056:5198914] -[VTapDatabaseManager insertRecord:withEncryption:] [Line 549] decryptedlog...vGuardDidFinishInitializing: status: 0, error: Error Domain=VGuardErrorDomain Code=20035 "VOS failed to read from trusted storage - (-8)" UserInfo={NSLocalizedDescription=VOS failed to read from trusted storage - (-8)}

				# check mismatch unsafe
				elif '-1006' in line:
					log_out_line_number("mismatch -> change bundleID correct in license", line, ["-1006"], "red", line_num, inputLogFile)
				# check JAILBREAK / Threat
				elif '1000 THREATNAME' in line or 'Jailbroken Detected by VOS' in line or '/Applications/Cydia.app' in line:
					log_out_line_number("jailbreak", line, ['1000 THREATNAME','jailbreak','Jailbroken Detected by VOS', 'Cydia'], "red", line_num, inputLogFile)
				# check Application_TAMPERING
				elif '2000 THREATNAME' in line or 'threatTypeId = 2000' in line:
					log_out_line_number("Remote Administration Tools", line, ["2000 THREATNAME"], "red", line_num, inputLogFile)
				# check Application_TAMPERING
				elif '3000 THREATNAME' in line or 'threatTypeId = 3000' in line:
					log_out_line_number("Application tampering", line, ["3000 THREATNAME"], "red", line_num, inputLogFile)
				# check RUNTIME_TAMPERING
				elif '4000 THREATNAME' in line or 'threatTypeId = 4000' in line:
					log_out_line_number("RUNTIME_TAMPERING", line, ["4000 THREATNAME"], "red", line_num, inputLogFile)
				# check Library tampering
				elif '5000 THREATNAME' in line or 'threatTypeId = 5000' in line:
					log_out_line_number("Library tampering", line, ["5000 THREATNAME"], "red", line_num, inputLogFile)
				# check -8 error -> restartvos
				elif 'Successfully completed executing VM, return -8' in line:
					log_out_line_number("-8 error", line, ["Successfully completed executing VM, return -8"], "red", line_num, inputLogFile)
				elif 'VOS failed to read from trusted storage - (-8)' in line:
					log_out_line_number("-8 error", line, ["VOS failed to read from trusted storage - (-8)"], "red", line_num, inputLogFile)
					# vtaplog: 19060	13BD0674AD9642B2F9163888D8038223ED10D886	16.3.1	*******(2b549be)	vGuardDidFinishInitializing: status: 0, error: Error Domain=VGuardErrorDomain Code=20035 "VOS failed to read from trusted storage - (-8)" UserInfo={NSLocalizedDescription=VOS failed to read from trusted storage - (-8)}	2023-07-08 02:42:43
					# vguardLog: 6124	13BD0674AD9642B2F9163888D8038223ED10D886	16.3.1/*******(91c6dcc)	VGUARD_STATUS : VGUARD_INITIALIZATION_FAILED, error VOS failed to read from trusted storage - (-8) - (20035)	2023-07-08 02:42:02
				# noted secure keyboard issue, fix later
				elif "Can't find keyplane that suppor" in line:
					log_out_line_number("secure keyboard issue", line, ["Can't find keyplane that suppor"], "red", line_num, inputLogFile)
				# check set URL inteligent
				elif 'URL: Threat_Intell_URL/threat-intel/send-threats-info' in line:
					log_out_line_number("Tommy do not have inteligent", line, ["URL: Threat_Intell_URL/threat-intel/send-threats-info"], "red", line_num, inputLogFile)
				# check SSL error ---> chagnge profile
				elif 'An SSL error has occurred and a secure connection to the server cannot be made' in line:
					log_out_line_number("SSL error", line, ["An SSL error has occurred and a secure connection to the server cannot be made"], "red", line_num, inputLogFile)
				# elif line.find('dfpHash') != -1:
				# 	log_out_line_number("dfpHash", line, ["dfpHash"], "red", line_num, inputLogFile)
				# check push noti register:
				elif 'pushNotificationRegister' in line and 'resultCode: 41014' in line:
					log_out_line_number("pushNotificationRegister success", line, ["pushNotificationRegister","41014"], "green", line_num, inputLogFile)
				elif 'pushNotificationRegister' in line and 'VTAP_INVALID_API_SEQUENCE' in line:
					log_out_line_number("pushNotificationRegister fail", line, ["pushNotificationRegister","VTAP_INVALID_API_SEQUENCE"], "red", line_num, inputLogFile)
				# check provision:
				elif 'provision/firmware?customer' in line and 'status=Success' in line or 'provision/firmware?customer' in line and 'responseCode:200' in line :
					log_out_line_number("provision success", line, ["provision","status=Success","responseCode:200","Status Code: 200", "&token="], "green", line_num, inputLogFile)
				# elif line.find('provision/firmware?customer') != -1 and line.find('status=Failure') != -1:
				# 	log_out_line_number("provision fail", line, ["provision"], "red", line_num, inputLogFile)
				elif 'provision/firmware?customer=' in line and 'responseCode:500' in line:
					log_out_line_number("provision fail", line, ["provision","responseCode:500","Status Code: 500","status=Failure"], "red", line_num, inputLogFile)
				# provisioning
				elif 'Process manifest succeeded' in line:
					log_out_line_number("token loaded in vTap source code... then continue to sendDeviceInfo process", line, ["Process manifest succeeded"], "green", line_num, inputLogFile)

				# check Ti request: [VTrackWebSvc sendMiscInfo]_block_invoke
				elif 'sendThreatInfo' in line and 'response' in line:
					log_out_line_number("Threat-intel sendThreatInfo [only in jailbreak / threat detect] -/threat-intel/send-threats-info", line, ["sendThreatInfo", "[VTrackWebSvc sendMiscInfo]_block_invoke","response:","Status Code:", "200", "response status Code", "threat-intel/"], "green", line_num, inputLogFile)
					# 2023-07-11 17:39:34.210682+0700 Test_Strong[328:12888] -[VTrackWebSvc sendThreatInfo:pHash:cHash:fullList:]_block_invoke [Line 473] response status Code: 200
					# 66, 1D03F00C476AEAEF3D0CE57D7229C35194F63183, 14.0.1/x.x.x.x(xxxxxxxx), sendThreatInfo - response: <NSHTTPURLResponse: 0x283764340> { URL: https://intranet.v-key.com:9613/threat-intel/send-threats-info } { Status Code: 200, Headers {
				
				# SSL pinning detecte
				elif 'SSL pinning detected for host' in line:
					log_out_line_number("SSL pinning =>> change profile", line, ["SSL pinning detected for host"], "red", line_num, inputLogFile)

				elif 'sendScanHeartbeat' in line and 'status' in line:
					log_out_line_number("VTrackWebSvc sendScanHeartbeat -/threat-intel/scan-heartbeat", line, ["VTrackWebSvc sendScanHeartbeat","sendScanHeartbeat", "status", "200"], "green", line_num, inputLogFile)

				elif 'sendDeviceInfo' in line and 'response' in line:
					log_out_line_number("Threat-intel sendDeviceInfo -/threat-intel/send-misc-information", line, ["sendDeviceInfo", "[VTrackWebSvc sendDeviceInfo]_block_invoke","response:","Status Code:", "200", "response status Code", "threat-intel/"], "green", line_num, inputLogFile)
					# 2023-07-11 17:39:33.516019+0700 Test_Strong[328:12896] -[VTrackWebSvc sendDeviceInfo]_block_invoke [Line 394] response status Code: 200
					# 30, 1D03F00C476AEAEF3D0CE57D7229C35194F63183, 14.0.1/x.x.x.x(xxxxxxxx), sendDeviceInfo - response status code: 200, 2023-07-11 17:39:33,

				elif 'sendAppInfo' in line and 'response' in line:
					if '(null)' in line:
						log_out_line_number("Threat-intel sendAppInfo -/threat-intel/send-app-information", line, ["sendAppInfo", "[VTrackWebSvc sendAppInfo]_block_invoke","response:","Status Code:", "null", "response status Code","threat-intel/send-app-information"], "red", line_num, inputLogFile)
					else:
						log_out_line_number("Threat-intel sendAppInfo -/threat-intel/send-app-information", line, ["sendAppInfo", "[VTrackWebSvc sendAppInfo]_block_invoke","response:","Status Code:", "200", "response status Code","threat-intel/send-app-information"], "green", line_num, inputLogFile)
						# 2023-07-11 17:39:33.777504+0700 Test_Strong[328:12896] -[VTrackWebSvc sendAppInfo]_block_invoke [Line 240] sendAppInfo - response: <NSHTTPURLResponse: 0x2837648a0> { URL: https://intranet.v-key.com:9613/threat-intel/send-app-information } { Status Code: 200, Headers {
						# 47, 1D03F00C476AEAEF3D0CE57D7229C35194F63183, 14.0.1/x.x.x.x(xxxxxxxx), sendAppInfo - response: <NSHTTPURLResponse: 0x2837648a0> { URL: https://intranet.v-key.com:9613/threat-intel/send-app-information } { Status Code: 200, Headers {
				
				elif 'sendMiscInfo' in line and 'response' in line:
					if '(null)' in line:
						log_out_line_number("Threat-intel sendMiscInfo -/threat-intel/send-misc-information", line, ["sendMiscInfo", "[VTrackWebSvc sendMiscInfo]_block_invoke","response:","Status Code:", "null", "response status Code", "threat-intel/send-misc-information"], "red", line_num, inputLogFile)
					else:
						log_out_line_number("Threat-intel sendMiscInfo -/threat-intel/send-misc-information", line, ["sendMiscInfo", "[VTrackWebSvc sendMiscInfo]_block_invoke","response:","Status Code:", "200", "response status Code", "threat-intel/send-misc-information"], "green", line_num, inputLogFile)
						# 2023-07-11 17:39:34.313863+0700 Test_Strong[328:12896] -[VTrackWebSvc sendMiscInfo]_block_invoke [Line 564] sendMiscInfo - response: <NSHTTPURLResponse: 0x283758240> { URL: https://intranet.v-key.com:9613/threat-intel/send-misc-information } { Status Code: 200, Headers { 
						# 68, 1D03F00C476AEAEF3D0CE57D7229C35194F63183, 14.0.1/x.x.x.x(xxxxxxxx), sendMiscInfo - response: <NSHTTPURLResponse: 0x283758240> { URL: https://intranet.v-key.com:9613/threat-intel/send-misc-information } { Status Code: 200, Headers {
						# 2023-07-11 10:35:50.404479+0700 test_Ti[315:6241] -[VTrackWebSvc sendMiscInfo]_block_invoke [Line 524] sendMiscInfo - response: (null) - request: <NSMutableURLRequest: 0x282467020> { URL: https:/stg.cloud.v-key.com/threat-intel/send-misc-information }
				#ok
				elif 'WEB_SERVICE_NAME - 0' in line and 'Response' in line:
					if ': 40' in line:
						log_out_line_number("WS_SIGNATURE /vtrack/webservice/mobile/signature/latest?", line, ["WEB_SERVICE_NAME - 0", "204","Response","Status Code", "40"], "red", line_num, inputLogFile)
					elif ': 20' in line:
						log_out_line_number("WS_SIGNATURE /vtrack/webservice/mobile/signature/latest?", line, ["WEB_SERVICE_NAME - 0", "204","Response","Status Code"], "green", line_num, inputLogFile)

				elif 'WEB_SERVICE_NAME - 1' in line and 'Response' in line:
					if ': 40' in line:
						log_out_line_number("WS_PROFILE /vtrack/webservice/mobile/profile?", line, ["WEB_SERVICE_NAME - 1", "204","Response","Status Code", "40"], "red", line_num, inputLogFile)
					elif ': 20' in line:
						log_out_line_number("WS_PROFILE /vtrack/webservice/mobile/profile?", line, ["WEB_SERVICE_NAME - 1", "204","Response","Status Code"], "green", line_num, inputLogFile)

				elif 'WEB_SERVICE_NAME - 2' in line and 'Response' in line:
					if ': 40' in line:
						log_out_line_number("WS_FIRMWARE /vtrack/webservice/mobile/firmware/latest?", line, ["WEB_SERVICE_NAME - 2", "204","Response","Status Code", "40"], "red", line_num, inputLogFile)
					elif ': 20' in line:
						log_out_line_number("WS_FIRMWARE /vtrack/webservice/mobile/firmware/latest?", line, ["WEB_SERVICE_NAME - 2", "204","Response","Status Code"], "green", line_num, inputLogFile)

				elif 'WEB_SERVICE_NAME - 3' in line and 'Response' in line:
					if ': 40' in line:
						log_out_line_number("WS_POST /vtrack/webservice/mobile/application/", line, ["WEB_SERVICE_NAME - 3", "204","Response","Status Code", "40"], "red", line_num, inputLogFile)
					elif ': 20' in line:
						log_out_line_number("WS_POST /vtrack/webservice/mobile/application/", line, ["WEB_SERVICE_NAME - 3", "204","Response","Status Code"], "green", line_num, inputLogFile)

				# MAYBE change to 4G to test before give feebBack:
				elif 'A server with the specified hostname could not be found' in line:
					log_out_line_number("change to 4G to test again", line, ["A server with the specified hostname could not be found"], "red", line_num, inputLogFile)
				
				# check TLA request: 
				elif 'troubleshooting-logs/public/send-log' in line:
					log_out_line_number("TLA request", line, ["troubleshooting-logs/public/send-log"], "green", line_num, inputLogFile)
				
				# check VPN issue
				elif 'sevrver response' in line and '-1012' in line:
					log_out_line_number("maybe VPN issues", line, ["Code=-1012"], "red", line_num, inputLogFile)

				# check resetVOSTrustedStorage api call in consoleLog
				elif 'resetVOSTrustedStorage' in line: # and line.find('successfully') != -1:
					log_out_line_number("resetVOSTrustedStorage", line, ["resetVOSTrustedStorage", "successfully"], "green", line_num, inputLogFile)
				# check Invalid vmHandle
				elif 'Invalid vmHandle' in line:
					log_out_line_number("Invalid vmHandle", line, ["Invalid vmHandle", "getPKITokenSerial"], "red", line_num, inputLogFile)
				#check vtap setTrustedTimeServerUrl
				elif 'setTrustedTimeServerUrl' in line and 'VTapManager configure' in line and 'status' in line:
					status = 'success' if 'status: 0' in line else 'fail'
					log_out_line_number(f"setTrustedTimeServerUrl {status}", line, ["VTapManager configure", "status"], "green", line_num, inputLogFile) if status == 'success' else log_out_line_number(f"setTrustedTimeServerUrl {status}", line, ["VTapManager configure", "status"], "red", line_num, inputLogFile)
				# check vtap sendDeviceInfoToServer
				elif 'VTapServerRequestManager sendDeviceInfoToServer' in line and 'Status Code' in line:
					log_out_line_number("VTapServerRequestManager sendDeviceInfoToServer", line, ["VTapServerRequestManager sendDeviceInfoToServer", "Status Code","200"], "green", line_num, inputLogFile)
				elif 'VTapServerRequestManager sendTroubleShootingLogWithData' in line and 'response' in line:
					log_out_line_number("VTapServerRequestManager sendTroubleShootingLog", line, ["VTapServerRequestManager sendTroubleShootingLogWithData", "response","200"], "green", line_num, inputLogFile)
				elif 'NullPointerException' in line:
					log_out_line_number("NullPointerException", line, ["NullPointerException"], "red", line_num, inputLogFile)

				# OTP action:
				elif 'generateTOTP : TOTP generation success' in line:
					log_out_line_number("ACTION generateTOTP success", line, ["generateTOTP","success"], "green", line_num, inputLogFile)
				# special check
				# PKI/ OTP logic
				elif 'removePKIFunction: aspDeregistrationResult:' in line:#remove asp
					log_out_line_number("remove asp", line, ["removePKIFunction: aspDeregistrationResult:"], "green", line_num, inputLogFile)
				# log sample: vtap.db-2025-07-25-02:07:08.log:4872  |   1148, 161A8746C136CB26BC133BB96D286C85980709D0, 16.7.11, 4.10.1.3(d3336f4), removePKIFunction: aspDeregistrationResult:41016, 2025-07-24 22:28:20, 
				elif 'smpDeRegister: is called' in line:#remove smp
					log_out_line_number("remove smp", line, ["smpDeRegister: is called"], "green", line_num, inputLogFile)
				elif 'pushNotificationDeRegister: result:' in line:#remove pushNotification
					log_out_line_number("remove pushNotification", line, ["pushNotificationDeRegister: result:"], "green", line_num, inputLogFile)
				# log sample: 210, 161A8746C136CB26BC133BB96D286C85980709D0, 16.7.11, 4.10.1.3(d3336f4), pushNotificationDeRegister: result:41017, 2025-07-24 15:46:10,
				elif 'pkiSendCsrSync:Erorr code is 409' in line:#409 server error
					log_out_line_number("409 server error", line, ["pkiSendCsrSync:Erorr code is 409"], "red", line_num, inputLogFile)
				elif 'NSURLErrorDomain error -1012' in line:#network error
					log_out_line_number("lỗi do server chưa enable smp cho k/h có CID đó", line, ["NSURLErrorDomain error -1012"], "red", line_num, inputLogFile)
				# log sample:  pushNotificationRegister: Post request returned error: The operation couldn’t be completed. (NSURLErrorDomain error -1012.), 2025-07-24 15:46:09,
				elif 'isPKIFunctionRegistered: funcID:0' in line:#check register ASP
					log_out_line_number("check is registered ASP", line, ["isPKIFunctionRegistered: funcID:0"], "green", line_num, inputLogFile)
				elif 'isPKIFunctionRegistered: funcID:1' in line:#check register SMP
					log_out_line_number("check is registered SMP", line, ["isPKIFunctionRegistered: funcID:1"], "green", line_num, inputLogFile)
				# MOMO
				elif 'signMsg' in line and '328205301' in line:
					log_out_line_number(" signMsg -328205301", line, ["signMsgChecksum","-328205301","signMsg"], "red", line_num, inputLogFile)
				elif 'VOS error' in line and '-180' in line: #case backup transfer data to new iphone 
					log_out_line_number("VOS error -180", line, ["VOS error","-180"], "red", line_num, inputLogFile)
				elif "couldn't retrieve trust anchor" in line:
					log_out_line_number("couldn't retrieve trust anchor", line, ["couldn't retrieve trust anchor"], "red", line_num, inputLogFile)
				elif 'V-OS Firmware' in line and 'PQR' in line:
					log_out_line_number("strong / light mode:", line, ["PQR)"], "yellow", line_num, inputLogFile)
				#2023-11-28 14:15:36.740211+0700 momo011[824:84978] D/taInterface: signMsgChecksum: call_vm_get_sign_msg_size() error -328205301
					
				# check key number of errorCode in line
				for x in error_fromLog.keys():
						if x in line and (not line[line.find(x)-1].isdigit() and not line[line.find(x) + len(x)].isdigit() ):
							if (x.find('-10009') != -1):
								log_out_line_number(error_fromLog[x]+" - maybe wrong token", line, [x], "red", line_num, inputLogFile)
							elif error_fromLog[x].find('_SUCCESS') == -1 and error_fromLog[x].find('_OK') == -1 and error_fromLog[x].find('VTAP_GREY_LISTED_DEVICE') == -1 and error_fromLog[x].find('VTAP_WHITE_LISTED_DEVICE') == -1:
								log_out_line_number(error_fromLog[x], line, [x], "red", line_num, inputLogFile)
							else:
								# if don't wanna show full line change "line" --> ""
								log_out_line_number(error_fromLog[x], line, [x], "green", line_num, inputLogFile)
				# check value of errorCode in line
				for x in error_fromLog.values():
						if x in line:
							if x.find('_SUCCESS') == -1 and x.find('_OK') == -1 and x.find('40302') == -1 and x.find('40300') == -1:
								log_out_line_number(x, line, [x], "red", line_num, inputLogFile)
							else:
								log_out_line_number(x, line, [x], "green", line_num, inputLogFile)

		# Print completion summary for this file
		print_separator("═", 80, BRIGHT_GREEN)
		print_section_header("FILE ANALYSIS COMPLETE", BRIGHT_GREEN)
		print(f"{BRIGHT_GREEN}✅ Finished processing: {BOLD}{inputLogFile}{RESET}")
		print(f"{BRIGHT_CYAN}📊 Total main log sections found: {BOLD}{main_count}{RESET}")

		# Print component versions summary if available
		if firmwareVersion != 0 or vosVersion != 0 or vguardVersion != 0 or vtapVersion != 0:
			print(f"\n{BRIGHT_YELLOW}📋 Component Versions Summary:{RESET}")
			if firmwareVersion != 0:
				print(f"   🔧 Firmware: {BRIGHT_WHITE}{firmwareVersion}{RESET}")
			if vosVersion != 0:
				print(f"   🖥️  VOS Processor: {BRIGHT_WHITE}{vosVersion}{RESET}")
			if vguardVersion != 0:
				print(f"   🛡️  VGuard Protection: {BRIGHT_WHITE}{vguardVersion}{RESET}")
			if vtapVersion != 0:
				print(f"   🔐 VTap Smart Token: {BRIGHT_WHITE}{vtapVersion}{RESET}")

		print()  # Add spacing between files

	# Final completion message
	print_separator("═", 80, BRIGHT_CYAN)
	print_section_header("ALL FILES PROCESSED", BRIGHT_CYAN)
	print(f"{BRIGHT_GREEN}🎉 Successfully analyzed {argument_count-1} file(s){RESET}\n")

# main from this code below
if __name__ == "__main__":
    main()
