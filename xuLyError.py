# Open the input file in read mode and output file in write mode
with open('pre_error.txt', 'r') as infile, open('filtered_error.txt', 'w') as outfile:
    # Iterate over each line in the input file
    for line in infile:
        # Split the line by tab character
        columns = line.split('\t')
        # Keep only the first two columns
        filtered_columns = columns[:2]
        # Join the filtered columns back into a string with tab separator and write to the output file
        outfile.write('\t'.join(filtered_columns) + '\n')

print("Filtered content has been written to 'filtered_error.txt'")
