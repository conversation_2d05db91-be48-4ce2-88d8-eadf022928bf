
----------------------------------------
VTAPERRORS
ID, REASON, LOG, TIMESTAMP
----------------------------------------

----------------------------------------
SQLITE_SEQUENCE
name, seq
----------------------------------------
VTAPLOGS, 327, 



----------------------------------------
VTAPLOGS
ID, TROUBLESHOOTING_ID, OSVER, SDKVER, LOG, TIMESTAMP
----------------------------------------
1, , 18.5, ********(d3336f4), ---MAIN LOG START---, 2025-07-25 11:46:56, 


2, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), None, 2025-07-25 11:46:56, 


3, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), ******** This V-OS Smart Token [RELEASE] SDK ********(d3336f4) was built on Jan 23 2025 at 22:35:58 ********, 2025-07-25 11:46:56, 


4, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), ---SUB LOG START---, 2025-07-25 11:46:56, 


5, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), setupVTap, 2025-07-25 11:46:56, 


6, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), statusVOS() status:1 error:(null), 2025-07-25 11:46:56, 


7, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), vGuardDidFinishInitializing: status: 1, error: (null), 2025-07-25 11:46:56, 


8, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), configure... setTrustedTimeServerUrl status: 0, 2025-07-25 11:46:57, 


9, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), ALLPROVISIONEDTOKEN_KEY is nil => Provisioning not done yet, 2025-07-25 11:46:57, 


10, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), configure() Not provisioned success:40200, 2025-07-25 11:46:57, 


11, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), ALLPROVISIONEDTOKEN_KEY is nil => Provisioning not done yet, 2025-07-25 11:46:57, 


12, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), vguardFlag: 0, 2025-07-25 11:46:57, 


13, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), statusVGuard() status:1 Error:(null), 2025-07-25 11:46:57, 


14, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:, 2025-07-25 11:46:58, 


15, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getLoadAckTokenFirmware, 2025-07-25 11:46:58, 


16, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), all provisioned list does not contain the token, 2025-07-25 11:46:58, 


17, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), downloadFileFromServer() request :<NSMutableURLRequest: 0x1392a7120> { URL: https://1070-prov.cloud.v-key.com/provision/firmware?customer=1070&dfp=ddcb850108ce433b576490461aa7eb82cacdd78f02eeae28139551d203060c3e&token=aCCEC9DF67&troubleshooting_id=3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703 }, 2025-07-25 11:46:58, 


18, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), downloadFileFromServer() fullpath:/private/var/mobile/Containers/Data/Application/F2DED608-9BF9-4D63-93DE-0D0427D44960/tmp/firmware.json, 2025-07-25 11:46:58, 


19, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), downloadFileFromServer() responseCode:200 response:<NSHTTPURLResponse: 0x1392a7220> { URL: https://1070-prov.cloud.v-key.com/provision/firmware?customer=1070&dfp=ddcb850108ce433b576490461aa7eb82cacdd78f02eeae28139551d203060c3e&token=aCCEC9DF67&troubleshooting_id=3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703 } { Status Code: 200, Headers {
    "Cache-Control" =     (
        "no-cache, no-store, max-age=0, must-revalidate"
    );
    Connection =     (
        "keep-alive"
    );
    "Content-Type" =     (
        "application/json"
    );
    Date =     (
        "Fri, 25 Jul 2025 04:46:59 GMT"
    );
    Expires =     (
        0
    );
    Pragma =     (
        "no-cache"
    );
    "Strict-Transport-Security" =     (
        "max-age=31536000; includeSubDomains"
    );
    "Transfer-Encoding" =     (
        Identity
    );
    "X-Content-Type-Options" =     (
        nosniff,
        nosniff
    );
    "X-Frame-Options" =     (
        DENY,
        DENY
    );
    "X-XSS-Protection" =     (
        0,
        "1; mode=block"
    );
} } VTap Response40600, 2025-07-25 11:47:00, 


20, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), all provisioned list does not contain the token, 2025-07-25 11:47:00, 


21, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), XXXXXclude:success /var/mobile/Containers/Data/Application/F2DED608-9BF9-4D63-93DE-0D0427D44960/Documents/aCCEC9DF67/vtapta.bin from backup (null), 2025-07-25 11:47:00, 


22, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), XXXXXclude:success /var/mobile/Containers/Data/Application/F2DED608-9BF9-4D63-93DE-0D0427D44960/Documents/aCCEC9DF67 from backup (null), 2025-07-25 11:47:00, 


23, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), This is not OTP Token: aCCEC9DF67, isTSFormatValid: 0, 2025-07-25 11:47:00, 


24, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:, 2025-07-25 11:47:00, 


25, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), loadToken:aCCEC9DF67 taId:3, 2025-07-25 11:47:00, 


26, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), TA tag:3 result:30000, 2025-07-25 11:47:00, 


27, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), loadTAWithFilePath:/var/mobile/Containers/Data/Application/F2DED608-9BF9-4D63-93DE-0D0427D44960/Documents/aCCEC9DF67/vtapta.bin taId:3, 2025-07-25 11:47:00, 


28, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), loadTAWithFilePath:/var/mobile/Containers/Data/Application/F2DED608-9BF9-4D63-93DE-0D0427D44960/Documents/aCCEC9DF67/vtapta.bin loadTAResult:2, 2025-07-25 11:47:00, 


29, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), loadToken : loadtoken/pkitoken is succeded now setting default token, 2025-07-25 11:47:00, 


30, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), setDefaultToken-- aCCEC9DF67 tokenType:3, 2025-07-25 11:47:00, 


31, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), setObject_vtap: iOS 11 and Above objAsData: {length = 148, bytes = 0x62706c69 73743030 d4010203 04050607 ... 00000000 00000067 } ...error: (null), 2025-07-25 11:47:00, 


32, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), This is not OTP Token: aCCEC9DF67, isTSFormatValid: 0, 2025-07-25 11:47:00, 


33, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), Process Manifest... processManifestResult:30000, 2025-07-25 11:47:00, 


34, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), Process manifest succeeded, 2025-07-25 11:47:00, 


35, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), This is not OTP Token: aCCEC9DF67, isTSFormatValid: 0, 2025-07-25 11:47:00, 


36, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:00, 


37, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), validateChecksum() error:fails for invalid char check, 2025-07-25 11:47:00, 


38, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), Checksum validation for token: aCCEC9DF67 with result: 0, 2025-07-25 11:47:00, 


39, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), setObject_vtap: iOS 11 and Above objAsData: {length = 387, bytes = 0x62706c69 73743030 d4010203 04050607 ... 00000000 00000117 } ...error: (null), 2025-07-25 11:47:00, 


40, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), loadAckTokenFirmware... allProvisionedTokens info updated-add, 2025-07-25 11:47:00, 


41, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), sendAckFirmwareToServerUsingTokenSerial ********** httpMethod: DELETE, 2025-07-25 11:47:00, 


42, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), sendAckFirmwareToServerUsingTokenSerial() request:<NSMutableURLRequest: 0x1392a7220> { URL: https://1070-prov.cloud.v-key.com/provision/firmware?customer=1070&token=aCCEC9DF67&dfp=ddcb850108ce433b576490461aa7eb82cacdd78f02eeae28139551d203060c3e&status=Success&troubleshooting_id=3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703 }, 2025-07-25 11:47:00, 


43, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), sendAckFirmwareToServerUsingTokenSerial() responseCode:200 response:<NSHTTPURLResponse: 0x1392a7f80> { URL: https://1070-prov.cloud.v-key.com/provision/firmware?customer=1070&token=aCCEC9DF67&dfp=ddcb850108ce433b576490461aa7eb82cacdd78f02eeae28139551d203060c3e&status=Success&troubleshooting_id=3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703 } { Status Code: 200, Headers {
    "Cache-Control" =     (
        "no-cache, no-store, max-age=0, must-revalidate"
    );
    Connection =     (
        "keep-alive"
    );
    "Content-Length" =     (
        0
    );
    Date =     (
        "Fri, 25 Jul 2025 04:47:01 GMT"
    );
    Expires =     (
        0
    );
    Pragma =     (
        "no-cache"
    );
    "Strict-Transport-Security" =     (
        "max-age=31536000; includeSubDomains"
    );
    "X-Content-Type-Options" =     (
        nosniff,
        nosniff
    );
    "X-Frame-Options" =     (
        DENY,
        DENY
    );
    "X-XSS-Protection" =     (
        0,
        "1; mode=block"
    );
} } error:(null), 2025-07-25 11:47:01, 


44, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getLoadAckTokenFirmware... sendAckFirmwareToServerUsingTokenSerial succeeded for positive ACK, 2025-07-25 11:47:01, 


45, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), load firmware with result:40600, 2025-07-25 11:47:01, 


46, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:01, 


47, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:01, 


48, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:01, 


49, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), This is not OTP Token: aCCEC9DF67, isTSFormatValid: 0, 2025-07-25 11:47:08, 


50, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), createPin ... storeUserPINResult TA Response:VTAP_TA_FUNC_OK VTap Response:VTAP_CREATE_PIN_SUCCESS, 2025-07-25 11:47:08, 


51, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), This is not OTP Token: aCCEC9DF67, isTSFormatValid: 0, 2025-07-25 11:47:08, 


52, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), checkPin ... checkPinesult TA Error:30000 VTap Error:40800, 2025-07-25 11:47:08, 


53, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:08, 


54, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:08, 


55, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:08, 


56, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:08, 


57, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:08, 


58, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getRegisteredUserId: userId is not present, 2025-07-25 11:47:08, 


59, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:08, 


60, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:08, 


61, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:08, 


62, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:08, 


63, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:08, 


64, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:08, 


65, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:08, 


66, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:08, 


67, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pushNotificationRegister: maskedPushToken: 6849dfd9 xxxx 5c904bb5, 2025-07-25 11:47:08, 


68, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:08, 


69, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getRegisteredUserId: userId is not present, 2025-07-25 11:47:08, 


70, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pushNotificationRegister: pnsType: -1, 2025-07-25 11:47:08, 


71, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:08, 


72, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pushNotificationRegister: Generated mobile JSON data error: (null), 2025-07-25 11:47:08, 


73, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pushNotificationRegister: request: <NSMutableURLRequest: 0x12f377fe0> { URL: https://1070-pki.cloud.v-key.com/smp/registration/token?customer=1070 }, 2025-07-25 11:47:08, 


74, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pushNotificationRegister: response code:200 response: <NSHTTPURLResponse: 0x13d0d8d80> { URL: https://1070-pki.cloud.v-key.com/smp/registration/token?customer=1070 } { Status Code: 200, Headers {
    "Cache-Control" =     (
        "no-cache, no-store, max-age=0, must-revalidate"
    );
    Connection =     (
        "keep-alive"
    );
    "Content-Type" =     (
        "application/json"
    );
    Date =     (
        "Fri, 25 Jul 2025 04:47:08 GMT"
    );
    Expires =     (
        0
    );
    Pragma =     (
        "no-cache"
    );
    "Strict-Transport-Security" =     (
        "max-age=31536000; includeSubDomains"
    );
    "Transfer-Encoding" =     (
        Identity
    );
    "X-Content-Type-Options" =     (
        nosniff,
        nosniff
    );
    "X-Frame-Options" =     (
        DENY,
        DENY
    );
    "X-XSS-Protection" =     (
        0,
        "1; mode=block"
    );
} }, 2025-07-25 11:47:08, 


75, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pushNotificationRegister: response Dict:{
    code = 5100;
    description = "Registration Request processed successfully";
    status = SUCCESS;
}, 2025-07-25 11:47:08, 


76, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pushNotificationRegister: json dict:{
    code = 5100;
    description = "Registration Request processed successfully";
    status = SUCCESS;
}, 2025-07-25 11:47:08, 


77, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pushNotificationRegister: resultCode :41014, 2025-07-25 11:47:08, 


78, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), Token Serial:, 2025-07-25 11:47:08, 


79, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:08, 


80, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:08, 


81, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:08, 


82, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:08, 


83, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:08, 


84, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), isPKIFunctionRegistered: funcID:0 not done yet, 2025-07-25 11:47:08, 


85, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), isPKIFunctionRegistered: userId pin is registered for functionid(0):0, 2025-07-25 11:47:08, 


86, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:08, 


87, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:08, 


88, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:08, 


89, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:08, 


90, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:08, 


91, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), isPKIFunctionRegistered: funcID:0 not done yet, 2025-07-25 11:47:08, 


92, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), isPKIFunctionRegistered: userId pin is registered for functionid(0):0, 2025-07-25 11:47:08, 


93, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:08, 


94, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:08, 


95, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:08, 


96, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:08, 


97, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:08, 


98, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:08, 


99, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:08, 


100, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:08, 


101, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:08, 


102, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:08, 


103, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), isPKIFunctionRegistered: funcID:0 not done yet, 2025-07-25 11:47:08, 


104, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), isPKIFunctionRegistered: userId pin is registered for functionid(0):0, 2025-07-25 11:47:08, 


105, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), Token Serial:, 2025-07-25 11:47:08, 


106, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:08, 


107, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:08, 


108, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:08, 


109, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:08, 


110, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:08, 


111, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getRegisteredUserId: userId is not present, 2025-07-25 11:47:08, 


112, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:08, 


113, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:08, 


114, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:08, 


115, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:08, 


116, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:08, 


117, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getRegisteredDeviceId: deviceId is not present, 2025-07-25 11:47:08, 


118, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:08, 


119, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), cdn : C=SG,ST=SG,L=SG,O=V-Key Pte Ltd,OU=V-OS,GN=(null),SN=(null),SERIALNUMBER=(null),CN=aCCEC9DF67,emailAddress=<EMAIL>, 2025-07-25 11:47:08, 


120, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), generateCsrAndSendSync: functionid=0..TA Response:VTAP_TA_FUNC_OK, 2025-07-25 11:47:08, 


121, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), generateCsrAndSendSync: csr, Signature, certchain OK, 2025-07-25 11:47:08, 


122, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:08, 


123, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pkiSendCsrSync: Generated mobile JSON data Error:(null), 2025-07-25 11:47:08, 


124, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pkiSendCsrSync:functionID:0 Request:<NSMutableURLRequest: 0x12f3774c0> { URL: https://1070-pki.cloud.v-key.com/asp/csr/sync-sign?customer=1070 }, 2025-07-25 11:47:08, 


125, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), Token Serial:, 2025-07-25 11:47:08, 


126, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), setObject_vtap: iOS 11 and Above objAsData: {length = 150, bytes = 0x62706c69 73743030 d4010203 04050607 ... 00000000 00000069 } ...error: (null), 2025-07-25 11:47:08, 


127, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pkiSendCsrSync: response code:200 response: <NSHTTPURLResponse: 0x13d0db8c0> { URL: https://1070-pki.cloud.v-key.com/asp/csr/sync-sign?customer=1070 } { Status Code: 200, Headers {
    "Cache-Control" =     (
        "no-cache, no-store, max-age=0, must-revalidate"
    );
    Connection =     (
        "keep-alive"
    );
    "Content-Type" =     (
        "application/json"
    );
    Date =     (
        "Fri, 25 Jul 2025 04:47:12 GMT"
    );
    Expires =     (
        0
    );
    Pragma =     (
        "no-cache"
    );
    "Strict-Transport-Security" =     (
        "max-age=31536000; includeSubDomains"
    );
    "Transfer-Encoding" =     (
        Identity
    );
    "X-Content-Type-Options" =     (
        nosniff,
        nosniff
    );
    "X-Frame-Options" =     (
        DENY,
        DENY
    );
    "X-XSS-Protection" =     (
        0,
        "1; mode=block"
    );
} }, 2025-07-25 11:47:12, 


128, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pkiSendCsrSync: json dict: {
    code = 4400;
    data = 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;
    description = "CSR Request processed successfully";
    status = SUCCESS;
}, 2025-07-25 11:47:12, 


129, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pkiCertDownload response code:-801259675 status:SUCCESS, 2025-07-25 11:47:12, 


130, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), saveCertData() functionID:0 certFileName:CERTfcee41b8-8811-4518-a077-488bfcc8719b, 2025-07-25 11:47:12, 


131, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:12, 


132, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), saveCertData: file not exists pth:/var/mobile/Containers/Data/Application/F2DED608-9BF9-4D63-93DE-0D0427D44960/Documents/aCCEC9DF67/CERTfcee41b8-8811-4518-a077-488bfcc8719b, 2025-07-25 11:47:12, 


133, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pkiCertDownload: certPath:/var/mobile/Containers/Data/Application/F2DED608-9BF9-4D63-93DE-0D0427D44960/Documents/aCCEC9DF67/CERTfcee41b8-8811-4518-a077-488bfcc8719b, 2025-07-25 11:47:12, 


134, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), verifySignature verificationResult:30000, 2025-07-25 11:47:12, 


135, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pkiCertDownload: isSignatureVerifiedByTA:1 and certPath:/var/mobile/Containers/Data/Application/F2DED608-9BF9-4D63-93DE-0D0427D44960/Documents/aCCEC9DF67/CERTfcee41b8-8811-4518-a077-488bfcc8719b, 2025-07-25 11:47:12, 


136, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:12, 


137, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), setObject_vtap: iOS 11 and Above objAsData: {length = 180, bytes = 0x62706c69 73743030 d4010203 04050607 ... 00000000 00000087 } ...error: (null), 2025-07-25 11:47:12, 


138, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), Token Serial:, 2025-07-25 11:47:12, 


139, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), generateCsrAndSendSync: result:41100, 2025-07-25 11:47:12, 


140, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), generateCsrAndSendSync: result:41100, 2025-07-25 11:47:12, 


141, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:12, 


142, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:12, 


143, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:12, 


144, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:12, 


145, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: CERTfcee41b8-8811-4518-a077-488bfcc8719b... error: (null), 2025-07-25 11:47:12, 


146, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: CERTfcee41b8-8811-4518-a077-488bfcc8719b... error: (null), 2025-07-25 11:47:12, 


147, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), setAuthKeyAsPregenKey: replacePreGenKeyResult:30000, 2025-07-25 11:47:12, 


148, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:12, 


149, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:12, 


150, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getRegisteredUserId: userId is not present, 2025-07-25 11:47:12, 


151, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:12, 


152, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getRegisteredDeviceId: deviceId is not present, 2025-07-25 11:47:12, 


153, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), vMessageAck: Generated mobile JSON data Error:(null), 2025-07-25 11:47:12, 


154, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), vMessageAck: response code:200 response: <NSHTTPURLResponse: 0x13d0daea0> { URL: https://1070-pki.cloud.v-key.com/asp/activation/certificate?customer=1070 } { Status Code: 200, Headers {
    "Cache-Control" =     (
        "no-cache, no-store, max-age=0, must-revalidate"
    );
    Connection =     (
        "keep-alive"
    );
    "Content-Type" =     (
        "application/json"
    );
    Date =     (
        "Fri, 25 Jul 2025 04:47:15 GMT"
    );
    Expires =     (
        0
    );
    Pragma =     (
        "no-cache"
    );
    "Strict-Transport-Security" =     (
        "max-age=31536000; includeSubDomains"
    );
    "Transfer-Encoding" =     (
        Identity
    );
    "X-Content-Type-Options" =     (
        nosniff,
        nosniff
    );
    "X-Frame-Options" =     (
        DENY,
        DENY
    );
    "X-XSS-Protection" =     (
        0,
        "1; mode=block"
    );
} }, 2025-07-25 11:47:15, 


155, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), vMessageAck: json dict: {
    code = 4450;
    description = "Activation status processed successfully";
    status = SUCCESS;
}, 2025-07-25 11:47:15, 


156, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), Replace pre key===================, 2025-07-25 11:47:15, 


157, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:15, 


158, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), setObject_vtap: iOS 11 and Above objAsData: {length = 139, bytes = 0x62706c69 73743030 d4010203 04050607 ... 00000000 0000005e } ...error: (null), 2025-07-25 11:47:15, 


159, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), Token Serial:, 2025-07-25 11:47:15, 


160, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:15, 


161, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:15, 


162, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:15, 


163, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:15, 


164, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:15, 


165, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), isPKIFunctionRegistered: funcID:1 not done yet, 2025-07-25 11:47:15, 


166, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), isPKIFunctionRegistered: userId pin is registered for functionid(1):0, 2025-07-25 11:47:15, 


167, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:15, 


168, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:15, 


169, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:15, 


170, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:15, 


171, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:15, 


172, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), isPKIFunctionRegistered: funcID:1 not done yet, 2025-07-25 11:47:15, 


173, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), isPKIFunctionRegistered: userId pin is registered for functionid(1):0, 2025-07-25 11:47:15, 


174, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:15, 


175, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:15, 


176, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:15, 


177, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:15, 


178, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:15, 


179, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:15, 


180, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:15, 


181, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:15, 


182, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:15, 


183, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:15, 


184, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), isPKIFunctionRegistered: funcID:1 not done yet, 2025-07-25 11:47:15, 


185, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), isPKIFunctionRegistered: userId pin is registered for functionid(1):0, 2025-07-25 11:47:15, 


186, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), Token Serial:, 2025-07-25 11:47:15, 


187, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:15, 


188, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:15, 


189, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:15, 


190, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:15, 


191, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:15, 


192, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: 1... error: (null), 2025-07-25 11:47:15, 


193, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), isPKIFunctionRegistered: funcID:0 is true, 2025-07-25 11:47:15, 


194, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), isPKIFunctionRegistered: userId pin is registered for functionid(0):1, 2025-07-25 11:47:15, 


195, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:15, 


196, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:15, 


197, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:15, 


198, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:15, 


199, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:15, 


200, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getRegisteredUserId: userId is not present, 2025-07-25 11:47:15, 


201, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:15, 


202, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:15, 


203, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:15, 


204, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:15, 


205, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:15, 


206, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getRegisteredDeviceId: deviceId is not present, 2025-07-25 11:47:15, 


207, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:15, 


208, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), cdn : C=SG,ST=SG,L=SG,O=V-Key Pte Ltd,OU=V-OS,GN=(null),SN=(null),SERIALNUMBER=(null),CN=aCCEC9DF67,emailAddress=<EMAIL>, 2025-07-25 11:47:15, 


209, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), generateCsrAndSendSync: functionid=1..TA Response:VTAP_TA_FUNC_OK, 2025-07-25 11:47:15, 


210, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), generateCsrAndSendSync: no cert data for function ID:1, 2025-07-25 11:47:15, 


211, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), generateCsrAndSendSync: csr, Signature, certchain OK, 2025-07-25 11:47:15, 


212, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:15, 


213, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pkiSendCsrSync: Generated mobile JSON data Error:(null), 2025-07-25 11:47:15, 


214, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pkiSendCsrSync:functionID:1 Request:<NSMutableURLRequest: 0x12f3779e0> { URL: https://1070-pki.cloud.v-key.com/asp/csr/sync-sign?customer=1070 }, 2025-07-25 11:47:15, 


215, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), Token Serial:, 2025-07-25 11:47:15, 


216, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), setObject_vtap: iOS 11 and Above objAsData: {length = 150, bytes = 0x62706c69 73743030 d4010203 04050607 ... 00000000 00000069 } ...error: (null), 2025-07-25 11:47:15, 


217, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pkiSendCsrSync: response code:409 response: <NSHTTPURLResponse: 0x13d0db0a0> { URL: https://1070-pki.cloud.v-key.com/asp/csr/sync-sign?customer=1070 } { Status Code: 409, Headers {
    "Cache-Control" =     (
        "no-cache, no-store, max-age=0, must-revalidate"
    );
    Connection =     (
        "keep-alive"
    );
    "Content-Type" =     (
        "application/json"
    );
    Date =     (
        "Fri, 25 Jul 2025 04:47:16 GMT"
    );
    Expires =     (
        0
    );
    Pragma =     (
        "no-cache"
    );
    "Strict-Transport-Security" =     (
        "max-age=31536000; includeSubDomains"
    );
    "Transfer-Encoding" =     (
        Identity
    );
    "X-Content-Type-Options" =     (
        nosniff,
        nosniff
    );
    "X-Frame-Options" =     (
        DENY,
        DENY
    );
    "X-XSS-Protection" =     (
        0,
        "1; mode=block"
    );
} }, 2025-07-25 11:47:16, 


218, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pkiSendCsrSync: json dict: {
    code = 4124;
    description = "Error occurred while checking certificate chain";
    status = FAILED;
}, 2025-07-25 11:47:16, 


219, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pkiSendCsrSync:Erorr code is 409, 2025-07-25 11:47:16, 


220, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), generateCsrAndSendSync: result:41101, 2025-07-25 11:47:16, 


221, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), generateCsrAndSendSync: result:41101, 2025-07-25 11:47:16, 


222, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:16, 


223, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:16, 


224, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:16, 


225, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:16, 


226, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:16, 


227, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getRegisteredUserId: userId is not present, 2025-07-25 11:47:16, 


228, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:16, 


229, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:16, 


230, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:16, 


231, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:16, 


232, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:16, 


233, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:16, 


234, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:16, 


235, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:16, 


236, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pushNotificationRegister: maskedPushToken: 6849dfd9 xxxx 5c904bb5, 2025-07-25 11:47:16, 


237, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:16, 


238, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getRegisteredUserId: userId is not present, 2025-07-25 11:47:16, 


239, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pushNotificationRegister: pnsType: -1, 2025-07-25 11:47:16, 


240, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:16, 


241, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pushNotificationRegister: Generated mobile JSON data error: (null), 2025-07-25 11:47:16, 


242, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pushNotificationRegister: request: <NSMutableURLRequest: 0x12f3765c0> { URL: https://1070-pki.cloud.v-key.com/smp/registration/token?customer=1070 }, 2025-07-25 11:47:16, 


243, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pushNotificationRegister: response code:200 response: <NSHTTPURLResponse: 0x13d0d96e0> { URL: https://1070-pki.cloud.v-key.com/smp/registration/token?customer=1070 } { Status Code: 200, Headers {
    "Cache-Control" =     (
        "no-cache, no-store, max-age=0, must-revalidate"
    );
    Connection =     (
        "keep-alive"
    );
    "Content-Type" =     (
        "application/json"
    );
    Date =     (
        "Fri, 25 Jul 2025 04:47:16 GMT"
    );
    Expires =     (
        0
    );
    Pragma =     (
        "no-cache"
    );
    "Strict-Transport-Security" =     (
        "max-age=31536000; includeSubDomains"
    );
    "Transfer-Encoding" =     (
        Identity
    );
    "X-Content-Type-Options" =     (
        nosniff,
        nosniff
    );
    "X-Frame-Options" =     (
        DENY,
        DENY
    );
    "X-XSS-Protection" =     (
        0,
        "1; mode=block"
    );
} }, 2025-07-25 11:47:16, 


244, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pushNotificationRegister: response Dict:{
    code = 5100;
    description = "Registration Request processed successfully";
    status = SUCCESS;
}, 2025-07-25 11:47:16, 


245, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pushNotificationRegister: json dict:{
    code = 5100;
    description = "Registration Request processed successfully";
    status = SUCCESS;
}, 2025-07-25 11:47:16, 


246, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pushNotificationRegister: resultCode :41014, 2025-07-25 11:47:16, 


247, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), Token Serial:, 2025-07-25 11:47:16, 


248, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:16, 


249, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:16, 


250, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:16, 


251, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:16, 


252, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:16, 


253, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: 1... error: (null), 2025-07-25 11:47:16, 


254, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), isPKIFunctionRegistered: funcID:0 is true, 2025-07-25 11:47:16, 


255, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), isPKIFunctionRegistered: userId pin is registered for functionid(0):1, 2025-07-25 11:47:16, 


256, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:16, 


257, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:16, 


258, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:16, 


259, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:16, 


260, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:16, 


261, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: 1... error: (null), 2025-07-25 11:47:16, 


262, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), isPKIFunctionRegistered: funcID:0 is true, 2025-07-25 11:47:16, 


263, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), isPKIFunctionRegistered: userId pin is registered for functionid(0):1, 2025-07-25 11:47:16, 


264, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:16, 


265, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:16, 


266, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:16, 


267, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:16, 


268, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:16, 


269, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:16, 


270, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:16, 


271, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:16, 


272, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:16, 


273, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:16, 


274, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: 1... error: (null), 2025-07-25 11:47:16, 


275, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), isPKIFunctionRegistered: funcID:0 is true, 2025-07-25 11:47:16, 


276, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), isPKIFunctionRegistered: userId pin is registered for functionid(0):1, 2025-07-25 11:47:16, 


277, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), generateCsrAndSendSync: provisioning not done or PKI token is not loaded and trying to send generateCsrAndSendSync request so SDK is sending resultCode = VTAP_INVALID_API_SEQUENCE, 2025-07-25 11:47:16, 


278, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), sendTroubleShootingLog.... Error while generating JSON data Error:(null), 2025-07-25 11:47:16, 


279, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:17, 


280, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:17, 


281, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:17, 


282, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:17, 


283, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:17, 


284, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:17, 


285, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), removeTokenFirmwareFiles:, 2025-07-25 11:47:17, 


286, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), This is not OTP Token: aCCEC9DF67, isTSFormatValid: 0, 2025-07-25 11:47:17, 


287, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:17, 


288, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:17, 


289, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), isPKIFunctionRegistered: funcID:1 not done yet, 2025-07-25 11:47:17, 


290, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), pushNotificationDeRegister: result:41017, 2025-07-25 11:47:17, 


291, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:17, 


292, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: 1... error: (null), 2025-07-25 11:47:17, 


293, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), isPKIFunctionRegistered: funcID:0 is true, 2025-07-25 11:47:17, 


294, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), aspUserIdDeRegister: Generated mobile JSON data Error:(null), 2025-07-25 11:47:17, 


295, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), aspUserIdDeRegister: response code:200 response: <NSHTTPURLResponse: 0x121cd3640> { URL: https://1070-pki.cloud.v-key.com/asp/registration/de-register?customer=1070 } { Status Code: 200, Headers {
    "Cache-Control" =     (
        "no-cache, no-store, max-age=0, must-revalidate"
    );
    Connection =     (
        "keep-alive"
    );
    "Content-Type" =     (
        "application/json"
    );
    Date =     (
        "Fri, 25 Jul 2025 04:47:20 GMT"
    );
    Expires =     (
        0
    );
    Pragma =     (
        "no-cache"
    );
    "Strict-Transport-Security" =     (
        "max-age=31536000; includeSubDomains"
    );
    "Transfer-Encoding" =     (
        Identity
    );
    "X-Content-Type-Options" =     (
        nosniff,
        nosniff
    );
    "X-Frame-Options" =     (
        DENY,
        DENY
    );
    "X-XSS-Protection" =     (
        0,
        "1; mode=block"
    );
} }, 2025-07-25 11:47:20, 


296, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), aspUserIdDeRegister: json dict: {
    code = 497;
    description = "De-Registration request processed successfully";
    status = SUCCESS;
}, 2025-07-25 11:47:20, 


297, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), aspUserIdDeRegister: is called 41016, 2025-07-25 11:47:20, 


298, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), removePKIFunction: aspDeregistrationResult:41016, 2025-07-25 11:47:20, 


299, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: 1... error: (null), 2025-07-25 11:47:20, 


300, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), removePKIFunction: failed to remove keypair hence we can not removePKIFunction for function ID :0 and tokenserial:aCCEC9DF67, 2025-07-25 11:47:20, 


301, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: 1... error: (null), 2025-07-25 11:47:20, 


302, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), removePKIFunction: removed auth pin entry, 2025-07-25 11:47:20, 


303, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: CERTfcee41b8-8811-4518-a077-488bfcc8719b... error: (null), 2025-07-25 11:47:20, 


304, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), getPKITokenSerial: Token Serial:aCCEC9DF67, 2025-07-25 11:47:20, 


305, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), deleteFileWithName: cert deleted with isFileDeleted:1, 2025-07-25 11:47:20, 


306, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), removePKIFunction: removed user Id, 2025-07-25 11:47:20, 


307, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), removePKIFunction: removed device Id, 2025-07-25 11:47:20, 


308, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), This is not OTP Token: aCCEC9DF67, isTSFormatValid: 0, 2025-07-25 11:47:20, 


309, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), unloadToken:aCCEC9DF67 taId:3, 2025-07-25 11:47:20, 


310, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), unloadToken: unloadTokenResult:0, 2025-07-25 11:47:20, 


311, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), unloadTA - 3, 2025-07-25 11:47:20, 


312, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:20, 


313, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:20, 


314, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), At least one token is provisioned..., 2025-07-25 11:47:20, 


315, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), VARemoveInstance result - 1, 2025-07-25 11:47:20, 


316, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), VAUnloadResult result - 1, 2025-07-25 11:47:20, 


317, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), removeTokenFirmwareFiles TS:aCCEC9DF67 - unloadToken: 1, 2025-07-25 11:47:20, 


318, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), deleteTsFolder : token:aCCEC9DF67, 2025-07-25 11:47:20, 


319, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:20, 


320, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:20, 


321, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:20, 


322, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:20, 


323, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: (
        {
        token = aCCEC9DF67;
    }
)... error: (null), 2025-07-25 11:47:20, 


324, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), removed token from provisioned list:aCCEC9DF67, 2025-07-25 11:47:20, 


325, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), removed provisioned list as last token :aCCEC9DF67 is removed from  list, 2025-07-25 11:47:20, 


326, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), objectForKey_vtap: iOS 11 and Above returnObj: aCCEC9DF67... error: (null), 2025-07-25 11:47:20, 


327, 3DDFFD4D56C41AA7C93D75DFD0F1BF54F5A63703, 18.5, ********(d3336f4), removeTokenFirmware... allProvisionedTokens info updated, 2025-07-25 11:47:20, 



----------------------------------------
VTAPENV
ID, DEVICE
----------------------------------------
1, iOS, iPhone14,4, 18.5, 


