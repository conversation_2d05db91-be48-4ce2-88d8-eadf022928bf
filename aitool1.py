#! /usr/bin/env python
"""
Log Checking Tool

Usage:
    python checkLogCSTool.py <log_file>

Highlights error codes in log files and writes logs to a Word document.
"""

import sys
import re
from termcolor import colored
from docx import Document
from docx.shared import RGBColor
from datetime import datetime
from urllib.parse import urlparse

# Define color mapping
COLOR_MAP = {
    'green': 'green',
    'yellow': 'yellow',
    'red': 'red',
}

# Precompile regex patterns for efficiency
ERROR_PATTERN = re.compile(r'error|fail|critical', re.IGNORECASE)
URL_PATTERN = re.compile(r'https?://\S+')


def highlight_text(text):
    """Highlights error-related words in logs."""
    if ERROR_PATTERN.search(text):
        return colored(text, COLOR_MAP['red'])
    return text


def extract_urls(text):
    """Extracts URLs from a given text."""
    return URL_PATTERN.findall(text)


def write_to_word(log_lines, output_file="log_report.docx"):
    """Writes the log content to a Word document."""
    doc = Document()
    doc.add_heading("Log Report", level=1)
    
    for line in log_lines:
        paragraph = doc.add_paragraph()
        run = paragraph.add_run(line)
        if ERROR_PATTERN.search(line):
            run.font.color.rgb = RGBColor(255, 0, 0)  # Highlight errors in red
    
    doc.save(output_file)
    print(f"Log report saved as {output_file}")


def process_log_file(log_file):
    """Reads and processes the log file."""
    try:
        with open(log_file, 'r', encoding='utf-8') as file:
            log_lines = file.readlines()
    except FileNotFoundError:
        print(f"Error: File '{log_file}' not found.")
        sys.exit(1)
    
    highlighted_lines = [highlight_text(line.strip()) for line in log_lines]
    write_to_word(log_lines)
    
    for line in highlighted_lines:
        print(line)


if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python checkLogCSTool.py <log_file>")
        sys.exit(1)
    
    log_file = sys.argv[1]
    process_log_file(log_file)
