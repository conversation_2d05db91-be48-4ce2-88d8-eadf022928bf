#! /usr/bin/env python

import pandas as pd
import os
import sys

# Check if any Excel file paths are provided as command-line arguments
argument_count = len(sys.argv)
if argument_count < 2:
    print("Please provide at least one path to an Excel file as a command-line argument.")
    sys.exit(1)

# Process each Excel file path provided
for i in range(1, argument_count):
    # Get the Excel file path from the command-line argument
    excel_file_path = sys.argv[i]

    # Load the Excel file
    try:
        excel_file = pd.ExcelFile(excel_file_path)
    except Exception as e:
        print(f"An error occurred while loading the Excel file: {e}")
        continue

    # Determine the logs sheet name based on the content of the Excel file
    if 'vguard' in excel_file_path:
        logs_sheet_name = 'logs'
    elif 'vtap' in excel_file_path:
        logs_sheet_name = 'VTAPLOGS'
    else:
        print(f"The Excel file '{excel_file_path}' does not contain the expected logs sheet.")
        continue

    # Read the logs sheet into a DataFrame
    try:
        logs_df = excel_file.parse(logs_sheet_name)
    except Exception as e:
        print(f"An error occurred while parsing the logs sheet in the file '{excel_file_path}': {e}")
        continue

    # Convert DataFrame to a string
    logs_text = logs_df.to_string(index=True)

    # Save the content to a text file in the same directory as the Excel file
    log_file_path = os.path.join(os.path.dirname(excel_file_path), f'{os.path.splitext(os.path.basename(excel_file_path))[0]}.log')

    try:
        with open(log_file_path, 'w+') as f:
            f.write(logs_text)
        print(f"Logs from '{excel_file_path}' have been saved to {log_file_path}")
    except Exception as e:
        print(f"An error occurred while saving the log file for '{excel_file_path}': {e}")

    #print out the content of the file
    # with open(log_file_path, 'r') as f:
    #  for line in f:
    #     print(line.strip())
