2025-07-25 11:47:11.log:1387 | 299, 3D0FFD4D56C41AA7C93D75DF0DF10F54F5A63703, 18.5, 4.10.1.3(d3336f4), pushNotificationDeRegister: result:41017, 2025-07-25 11:47:17,
vtap.db-2025-07-25-05:01:11.log:1387 | 299, 3D0FFD4D56C41AA7C93D75DF0DF10F54F5A63703, 18.5, 4.10.1.3(d3336f4), pushNotificationDeRegister: result:41017, 2025-07-25 11:47:17,
[*] checking registered ASP
vtap.db-2025-07-25-05:01:11.log:1396 | 293, 3D0FFD4D56C41AA7C93D75DF0DF10F54F5A63703, 18.5, 4.10.1.3(d3336f4), isPKIFunctionRegistered: funcID:0 is true, 2025-07-25 11:47:17,
[*] VTAP_PUSH_NOTIFICATION_DEREGISTRATION_SUCCESS
vtap.db-2025-07-25-05:01:11.log:1449 | 297, 3D0FFD4D56C41AA7C93D75DF0DF10F54F5A63703, 18.5, 4.10.1.3(d3336f4), aspDeregistrationResult: is called 41016, 2025-07-25 11:47:20,
---MAIN LOG START---
VGuardManager start ...
V-OS Firmware Version: 4.10.1.3(d3336f4) [DEBUG] PQR
V-OS Processor Version: 4.10.1.3(d3336f4) [RELEASE]
V-OS App Protection SDK 4.10.1.3(d3336f4) [RELEASE]
V-OS Smart Token SDK 4.10.1.3(d3336f4) [DEBUG]
customerID: 12345
logger_set_tid(): ABC123DEF456
Crypto Mode = 1
Managebility: 1
ALLPROVISIONEDTOKEN_KEY is nil
VOS init successfully
VGUARD_INITIALIZATION_SUCCEEDED
statusVOS: 1
VGUARD_STATUS: 1 - safe
provision/firmware?customer=12345&token=xyz status=Success responseCode:200
Process manifest succeeded
sendThreatInfo response Status Code: 200
sendDeviceInfo response Status Code: 200
sendAppInfo response Status Code: 200
sendMiscInfo response Status Code: 200
generateTOTP : TOTP generation success
removePKIFunction: aspDeregistrationResult:41016
smpDeRegister: is called
pushNotificationDeRegister: result:41017
VOS error -180
couldn't retrieve trust anchor
signMsg -328205301
