#! /usr/bin/env python
# Script to check log file and highlight error codes
# Usage: python checkLogCSTool.py <log file>

import os
import sys
import subprocess
import platform
import argparse
import re

# Clear the command line based on the OS
def clear_console():
    if platform.system() == "Windows":
        os.system("cls")
    else:
        os.system("clear")

clear_console()

# Function to install missing dependencies
def install_package(package):
    try:
        __import__(package)
    except ImportError:
        subprocess.run([sys.executable, "-m", "pip", "install", package], check=True)

# Ensure required packages are installed
install_package("termcolor")
install_package("python-docx")

from termcolor import colored
from docx import Document

# Argument parser for command-line handling
parser = argparse.ArgumentParser(description="Check log file and highlight error codes.")
parser.add_argument("logfile", type=str, help="Path to the log file")

args = parser.parse_args()

# Verify log file exists
if not os.path.exists(args.logfile):
    print(colored(f"Error: File '{args.logfile}' not found.", "red"))
    sys.exit(1)

# Function to clean log lines by removing NULL bytes and control characters
def clean_text(text):
    return re.sub(r'[\x00-\x1F]+', ' ', text)  # Replace control characters with space

# Function to process log file
def process_log_file(logfile):
    with open(logfile, "r", encoding="utf-8", errors="replace") as file:
        lines = file.readlines()

    document = Document()
    for line in lines:
        cleaned_line = clean_text(line.strip())  # Clean non-XML-compatible characters

        if "ERROR" in cleaned_line:
            print(colored(cleaned_line, "red"))
            document.add_paragraph(cleaned_line, style="Heading 1")
        else:
            print(cleaned_line)
            document.add_paragraph(cleaned_line)

    document.save("LogReport.docx")
    print(colored("Log file processing completed. Report saved as 'LogReport.docx'", "green"))

# Process the log file
process_log_file(args.logfile)
