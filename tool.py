#! /usr/bin/env python
# redme: check log file and highlight error code
# run command: python tommyChecklog.py <log file>

import os
import sys
import subprocess
import re
from urllib.parse import urlparse
from datetime import datetime
import requests
from termcolor import colored
from docx import Document
from docx.shared import RGBColor

# Define the color mapping for 'green', 'yellow', and 'blue'
color_map = {
    'green': RGBColor(0, 128, 0),  # Dark green
    'blue': RGBColor(0, 0, 255),  # Blue
    'red': RGBColor(255, 0, 0),  # Red
    'yellow': RGBColor(255, 165, 0)  # Orange
}
# Global variable declaration
main_count = 0
LOG_FILE = "Result.docx"
ERROR_CODES_FILE = '/Users/<USER>/Documents/Log/filtered_error.txt'

inputLogFile = ""
error_fromLog = dict()
firmwareVersion = 0
vosVersion = 0
vguardVersion = 0
vtapVersion = 0
# variable to check conflict Debug, Release SDK
debugFirmware = 'N/A'
debugVOS = 'N/A'
debugVguard = 'N/A'
debugVtap = 'N/A'

# Define ANSI escape codes for text colors
RED_TEXT = "\033[31m"
YELLOW_BG = "\033[43m"
RESET = "\033[0m"  # Reset text formatting

# Clear the command line
os.system('clear')

# This function checks for conflicts in the SDK based on the log line provided.
def check_conflict_SDK_conflict(log_line):
    # Declare global variables that store the debug status of different components.
    global debugFirmware, debugVOS, debugVguard, debugVtap

    # Create a dictionary to map the log line keys to the corresponding global variables.
    debug_dict = {
        'V-OS Processor': 'debugVOS',
        'V-OS Firmware': 'debugFirmware',
        'App Protection': 'debugVguard',
        'V-OS Smart Token': 'debugVtap'
    }

    # Iterate over each key-value pair in the dictionary.
    for key, value in debug_dict.items():
        # If the key is found in the log line and 'initVOSWithCompletion' is not in the log line,
        # check if any form of the word 'debug' is in the log line.
        if key in log_line and 'initVOSWithCompletion' not in log_line:
            # If 'debug' is found, set the corresponding global variable to 'debug'.
            # Otherwise, set it to 'release'.
            globals()[value] = 'debug' if any(d in log_line for d in ['DEBUG', 'debug', 'Debug']) else 'release'

    # Construct a string that contains the debug status of all components.
    outputString = f"\n    VOS:{debugVOS}\n    Vguard:{debugVguard}\n    Firmware:{debugFirmware}\n    Vtap:{debugVtap}\n"
    
    # If none of the debug statuses are 'N/A', log the output string along with some additional information.
    if debugVOS != 'N/A' and debugVguard != 'N/A' and debugFirmware != 'N/A':
        color_wordsTest(f"S##### --> Vkey Component info:{outputString}", {'debug':'red', 'release':'green','S##### --> Vkey Component info:':'yellow'})

def get_version_from_log(log_line):
    version_pattern = None
    component = None

    # Determine the appropriate version pattern and component based on the log line
    if 'V-OS Processor' in log_line:
        version_pattern = r'Version: (\d+\.\d+\.\d+\.\d+)'
        component = 'V-OS Processor'
    elif 'V-OS Firmware' in log_line:
        version_pattern = r'Version: (\d+\.\d+\.\d+\.\d+)'
        component = 'V-OS Firmware'
    elif 'App Protection' in log_line:
        version_pattern = r'SDK (\d+\.\d+\.\d+\.\d+)'
        component = 'App Protection'
    elif 'V-OS Smart Token' in log_line:
        version_pattern = r'SDK (\d+\.\d+\.\d+\.\d+)'
        component = 'V-OS Smart Token'
    
    if not version_pattern:
        return
    
    match = re.search(version_pattern, log_line)
    if match:
        # Reference global variables
        global firmwareVersion, vosVersion, vguardVersion, vtapVersion
        version = match.group(1) if match.group(1) else 0
        
        if component == 'V-OS Processor':
            color_word_withBackground(YELLOW_BG, RED_TEXT, colored(f"- old vos processor: {vosVersion}", "black"))
            vosVersion = version
            color_word_withBackground(YELLOW_BG, RED_TEXT, colored(f"+ new vos processor: {vosVersion}", "green"))
        elif component == 'V-OS Firmware':
            color_word_withBackground(YELLOW_BG, RED_TEXT, colored(f"- old firmware: {firmwareVersion}", "black"))
            firmwareVersion = version
            color_word_withBackground(YELLOW_BG, RED_TEXT, colored(f"+ new firmware: {firmwareVersion}", "green"))
        elif component == 'App Protection':
            color_word_withBackground(YELLOW_BG, RED_TEXT, colored(f"- old vguard protection: {vguardVersion}", "black"))
            vguardVersion = version
            color_word_withBackground(YELLOW_BG, RED_TEXT, colored(f"+ new vguard protection: {vguardVersion}", "green"))
        elif component == 'V-OS Smart Token':
            color_word_withBackground(YELLOW_BG, RED_TEXT, colored(f"- old vtap: {vtapVersion}", "black"))
            vtapVersion = version
            color_word_withBackground(YELLOW_BG, RED_TEXT, colored(f"+ new vtap: {vtapVersion}", "green"))

def color_words(string, words_to_color, color):
    colored_string = string
    for word in words_to_color:
        colored_string = colored_string.replace(word, colored(word, color))
    print(colored_string)

def color_wordsTest(string, words_to_color):
    colored_string = string
    for word, color in words_to_color.items():
        colored_string = colored_string.replace(word, colored(word, color))
    print(colored_string)

def color_word_withBackground(background_color, text_color, string):
    print(background_color + text_color + f"{string}" + RESET)
    
def makeOutputLink():
    print(colored(f"File output :{LOG_FILE}", "magenta"))
    # Uncomment to open file when enabled
    # os.system(f'open {LOG_FILE}')

def hello():
    big_text = """
 _   _      _ _
| | | |    | | |
| |_| | ___| | | ___
|  _  |/ _ \ | |/ _ \\
| | | |  __/ | |  __/
\_| |_/\___|_|_|\___|
"""
    print(big_text)

def check_docx_file():
    if not os.path.exists(LOG_FILE):
        Document().save(LOG_FILE)

def download_file_from_url(url, filename):
    try:
        response = requests.get(url)
        if response.status_code == 200:
            with open(filename, "wb") as file:
                file.write(response.content)
            return True
        else:
            return False
    except Exception as e:
        print(f"Error downloading file from URL: {e}")
        return False

def log_out_line_number(error_message, line, highlight_word, color, linenum, inputLogFile):
    if color in ("green", "yellow", "blue"):
        print(colored(f"S##### --> {error_message}:", color))
    else: # red color
        print(colored(f"E##### --> {error_message}:", color))
    # Construct output string
    outputString = inputLogFile+":"+str(linenum)+"  |   "+line
    color_words(outputString, highlight_word, color)
    
    # Uncomment to save to docx file when needed
    # Save result to .docx file
    # check_docx_file()
    # document = Document(LOG_FILE)
    # p = document.add_paragraph('')
    # run = p.add_run(f"{error_message}")
    # run.bold = True
    # run.font.color.rgb = color_map.get(color, color_map["red"])
    # document.add_paragraph(line)
    # document.save(LOG_FILE)
    
def load_error_codes():
    try:
        with open(ERROR_CODES_FILE, "r") as file:
            return dict(line.strip().split() for line in file)
    except FileNotFoundError:
        print(f"Error: File '{ERROR_CODES_FILE}' not found.")
        return {}  # Return an empty dictionary on error

def openLogFile(name, line_number=1, column=1):
    # Open the file in VSCode at a specific line and column
    FileToOpen = f'code -g {name}:{line_number}:{column}'
    print(FileToOpen)
    os.system(FileToOpen)

def extract_filename_from_url(url):
    parsed_url = urlparse(url)
    filename = os.path.basename(parsed_url.path)
    return filename

def main():
    error_fromLog = load_error_codes() # load error list from storage
    argument_count = len(sys.argv)
    print("Number of file input: ", argument_count-1)
    
    # Dictionary of patterns to check in logs with their corresponding actions
    log_patterns = {
        # SDK Version patterns
        'V-OS Firmware': {"message": "SDK Firmware Version", "keywords": ["V-OS Firmware","[DEBUG]","PQR","Debug","Release"], "color": "yellow", "check_version": True, "check_conflict": True},
        'V-OS Processor': {"message": "SDK VOS Version", "keywords": ["V-OS Processor", "[RELEASE]", "[DEBUG]", "Debug", "V-OS Processor", "Release"], "color": "yellow", "check_version": True, "check_conflict": True},
        'V-OS App Protection': {"message": "SDK App Protection version", "keywords": ["V-OS App Protection", "[RELEASE]","[DEBUG]"], "color": "yellow", "check_version": True, "check_conflict": True},
        'V-OS Smart Token': {"message": "SDK Smart Token version", "keywords": ["V-OS Smart Token","[DEBUG]"], "color": "yellow", "check_version": True, "check_conflict": True},
        
        # Server and configuration patterns
        'Server Version >= 4.8': {"message": "Server Version >= 4.8", "keywords": ["Server Version >= 4.8"], "color": "yellow"},
        'updateCheckInterval': {"message": "updateCheckInterval", "keywords": ["updateCheckInterval"], "color": "yellow"},
        'Version lower than 4.8': {"message": "Version lower than 4.8", "keywords": ["Version lower than 4.8"], "color": "yellow"},
        'customerID': {"message": "customerID", "keywords": ["customerID", "startVOS"], "color": "yellow"},
        'logger_set_tid()': {"message": "VOS -->Console log --> logger_set_tid:", "keywords": ["logger_set_tid():"], "color": "yellow"},
        'Crypto Mode': {"message": "Crypto Mode", "keywords": ["Crypto Mode"], "color": "yellow"},
        'Managebility:': {"message": lambda line: "Asset Managebility Mode = 1" if "Managebility: 1" in line else "Asset Managebility Mode = 0", "keywords": ["Managebility: 0","Managebility: 1"], "color": "yellow"},
        
        # Status checks
        'MAIN LOG START---': {"message": lambda line, count: f"---MAIN LOG START: {count} ------------------------------------------------", "keywords": ["---MAIN LOG START---"], "color": "blue", "increment_count": True},
        'com.google.android': {"message": "ANDROID log", "keywords": ['com.google.android'], "color": "yellow", "condition": lambda count: count == 0},
        'The file "firmware" could': {"message": 'Missing "firmware"', "keywords": ['"firmware"','no such file','/firmware','No such file or directory'], "color": "red"},
        'Profile decryption failed': {"message": 'Profile decryption failed"', "keywords": ["Profile decryption failed"], "color": "red"},
        'VGuardManager start ...': {"message": "start Vguard", "keywords": ["VGuardManager start ..."], "color": "green"},
        
        # VOS Status
        'statusVOS': {"message": lambda line: 'VOS_NOTOK' if any(x in line for x in ['VOSSTATUS: 0', 'statusVOS: 0', 'status:0']) else 'VOS_OK', 
                     "keywords": lambda msg: [f"{msg}", "VOSSTATUS", "statusVOS"], 
                     "color": lambda msg: "red" if msg == 'VOS_NOTOK' else "green"},
        'statusVOS.*VOS_OK': {"message": "VOS_OK", "keywords": ["VOS_OK"], "color": "green"},
        
        # More patterns from existing code...
        '[VGuardManager initVOSWithCompletion:]_block_invoke.*VOS init successfully': {"message": "VOS init successfully", "keywords": ["VOS init successfully"], "color": "green"},
        'init vos fail': {"message": "VOS init fail", "keywords": ["init vos fail","[VGuardManager initVOSWithCompletion"], "color": "red"},
        '[VGuardManager initializeVGuard].*initiate VguardManager': {"message": "start VGuardManager initializeVGuard", "keywords": ["VGuardManager initializeVGuard"], "color": "green"},
        
        # VGUARD status
        'VGUARD_STATUS - 0|V-Guard status: 0|VGUARD_UNSAFE': {"message": "VGUARD_STATUS: 0 - unsafe", "keywords": ["VGUARD_STATUS - 0","V-Guard status: 0","VGUARD_UNSAFE"], "color": "red"},
        'statusVGuard\(\) status:1|V-Guard status: 1': {"message": "VGUARD_STATUS: 1 - safe", "keywords": ["statusVGuard() status:1", "V-Guard status: 1"], "color": "green"},
        'V-Guard status: 2': {"message": "VGUARD_UNDETERMINE -2 ", "keywords": ["V-Guard status: 2"], "color": "red"},
        'VGUARD_INITIALIZATION_SUCCEEDED': {"message": "VGUARD_INITIALIZATION_SUCCEEDED", "keywords": ["VGUARD_INITIALIZATION_SUCCEEDED"], "color": "green"},
        'VGUARD_INITIALIZATION_FAILED': {"message": "VGUARD_INITIALIZATION_FAILED", "keywords": ["VGUARD_INITIALIZATION_FAILED"], "color": "red"},

        # Error and threat patterns
        '-1006': {"message": "mismatch -> change bundleID correct in license", "keywords": ["-1006"], "color": "red"},
        '1000 THREATNAME|Jailbroken Detected by VOS|/Applications/Cydia.app': {"message": "jailbreak", "keywords": ['1000 THREATNAME','jailbreak','Jailbroken Detected by VOS', 'Cydia'], "color": "red"},
        '2000 THREATNAME|threatTypeId = 2000': {"message": "Remote Administration Tools", "keywords": ["2000 THREATNAME"], "color": "red"},
        '3000 THREATNAME|threatTypeId = 3000': {"message": "Application tampering", "keywords": ["3000 THREATNAME"], "color": "red"},
        '4000 THREATNAME|threatTypeId = 4000': {"message": "RUNTIME_TAMPERING", "keywords": ["4000 THREATNAME"], "color": "red"},
        '5000 THREATNAME|threatTypeId = 5000': {"message": "Library tampering", "keywords": ["5000 THREATNAME"], "color": "red"},
        
        # More error patterns
        'Successfully completed executing VM, return -8': {"message": "-8 error", "keywords": ["Successfully completed executing VM, return -8"], "color": "red"},
        'VOS failed to read from trusted storage - \(-8\)': {"message": "-8 error", "keywords": ["VOS failed to read from trusted storage - (-8)"], "color": "red"},
        "Can't find keyplane that suppor": {"message": "secure keyboard issue", "keywords": ["Can't find keyplane that suppor"], "color": "red"},
        'URL: Threat_Intell_URL/threat-intel/send-threats-info': {"message": "Tommy do not have inteligent", "keywords": ["URL: Threat_Intell_URL/threat-intel/send-threats-info"], "color": "red"},
        'An SSL error has occurred and a secure connection to the server cannot be made': {"message": "SSL error", "keywords": ["An SSL error has occurred and a secure connection to the server cannot be made"], "color": "red"},
        
        # Push notification and provisioning
        'pushNotificationRegister.*resultCode: 41014': {"message": "pushNotificationRegister success", "keywords": ["pushNotificationRegister","41014"], "color": "green"},
        'pushNotificationRegister.*VTAP_INVALID_API_SEQUENCE': {"message": "pushNotificationRegister fail", "keywords": ["pushNotificationRegister","VTAP_INVALID_API_SEQUENCE"], "color": "red"},
        'provision/firmware\?customer.*status=Success|provision/firmware\?customer.*responseCode:200': {"message": "provision success", "keywords": ["provision","status=Success","responseCode:200","Status Code: 200", "&token="], "color": "green"},
        'provision/firmware\?customer=.*responseCode:500': {"message": "provision fail", "keywords": ["provision","responseCode:500","Status Code: 500","status=Failure"], "color": "red"},
        'Process manifest succeeded': {"message": "token loaded in vTap source code... then continue to sendDeviceInfo process", "keywords": ["Process manifest succeeded"], "color": "green"},
        
        # Threat intelligence
        'sendThreatInfo.*response': {"message": "Threat-intel sendThreatInfo [only in jailbreak / threat detect]", "keywords": ["sendThreatInfo", "[VTrackWebSvc sendMiscInfo]_block_invoke","response:","Status Code:", "200", "response status Code", "threat-intel/"], "color": "green"},
        'SSL pinning detected for host': {"message": "SSL pinning =>> change profile", "keywords": ["SSL pinning detected for host"], "color": "red"},
        'sendScanHeartbeat.*status': {"message": "VTrackWebSvc sendScanHeartbeat", "keywords": ["VTrackWebSvc sendScanHeartbeat","sendScanHeartbeat", "status", "200"], "color": "green"},
        'sendDeviceInfo.*response': {"message": "Threat-intel sendDeviceInfo", "keywords": ["sendDeviceInfo", "[VTrackWebSvc sendDeviceInfo]_block_invoke","response:","Status Code:", "200", "response status Code", "threat-intel/"], "color": "green"},
        
        # More patterns...
    }
    
    for i in range(1, len(sys.argv)):
        hello()
        # Reset global variables
        global main_count, debugFirmware, debugVguard, debugVOS, debugVtap
        global firmwareVersion, vosVersion, vguardVersion, vtapVersion
        
        main_count = 0
        debugFirmware = debugVguard = debugVOS = debugVtap = 'N/A'
        firmwareVersion = vosVersion = vguardVersion = vtapVersion = 0

        inputLogFile = sys.argv[i]
        check_docx_file() # check if file exists
        
        # Handle URL inputs
        if inputLogFile.startswith(("http://", "https://")):
            filename = extract_filename_from_url(inputLogFile)
            if download_file_from_url(inputLogFile, filename):
                inputLogFile = filename
            else:
                print(f"Failed to download the file from URL: {inputLogFile}")
                continue
                
        print(colored(f"File input: {inputLogFile}", "magenta"))
        # openLogFile(inputLogFile) # open log file to check
        
        try:
            with open(inputLogFile, 'r') as file:
                lines = file.readlines()
                
                for line_num, line in enumerate(lines, start=1):
                    matched = False
                    
                    # Check patterns in the dictionary
                    for pattern, details in log_patterns.items():
                        if re.search(pattern, line):
                            matched = True
                            
                            # Handle special actions
                            if details.get("increment_count", False):
                                main_count += 1
                                message = details["message"](line, main_count) if callable(details["message"]) else details["message"]
                            elif "condition" in details and not details["condition"](main_count):
                                continue  # Skip if condition not met
                            else:
                                message = details["message"](line) if callable(details["message"]) else details["message"]
                            
                            # Get keywords and color
                            keywords = details["keywords"](message) if callable(details["keywords"]) else details["keywords"]
                            color = details["color"](message) if callable(details["color"]) else details["color"]
                            
                            # Log the message
                            log_out_line_number(message, line, keywords, color, line_num, inputLogFile)
                            
                            # Check SDK version and conflicts if needed
                            if details.get("check_version", False):
                                get_version_from_log(line)
                            if details.get("check_conflict", False):
                                check_conflict_SDK_conflict(line)
                            
                            break
                    
                    # If no match found in patterns, check error codes
                    if not matched:
                        # Check for error codes
                        for code, description in error_fromLog.items():
                            if code in line and (line.find(code) == 0 or not line[line.find(code)-1].isdigit()) and (line.find(code) + len(code) >= len(line) or not line[line.find(code) + len(code)].isdigit()):
                                is_success = any(success in description for success in ['_SUCCESS', '_OK', 'VTAP_GREY_LISTED_DEVICE', 'VTAP_WHITE_LISTED_DEVICE'])
                                color = "green" if is_success else "red"
                                message = f"{description} - maybe wrong token" if code == '-10009' else description
                                log_out_line_number(message, line, [code], color, line_num, inputLogFile)
                                matched = True
                                break
                        
                        # Check for error descriptions if still no match
                        if not matched:
                            for description in error_fromLog.values():
                                if description in line:
                                    is_error = not any(success in description for success in ['_SUCCESS', '_OK']) or any(err in description for err in ['40302', '40300'])
                                    color = "red" if is_error else "green"
                                    log_out_line_number(description, line, [description], color, line_num, inputLogFile)
                                    break
                    
                    # Add other special case handling here if needed
                
        except FileNotFoundError:
            print(f"Error: File '{inputLogFile}' not found.")
            continue
        except UnicodeDecodeError:
            print(f"Error: Could not decode file '{inputLogFile}'. It may be a binary file.")
            continue
        except Exception as e:
            print(f"Error processing file '{inputLogFile}': {e}")
            continue
            
    makeOutputLink() # Enable when writing to Excel file

# Run main function when script is executed directly
if __name__ == "__main__":
    main()
