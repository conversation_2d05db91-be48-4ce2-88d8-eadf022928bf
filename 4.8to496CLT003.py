setSecureLoggerTID: random TID
logger_set_tid()
logger_set_tid(): e27a851ea649ab909bbe13fb6b864486b5d07f80
vos_read_tid_from_stable_tid_file(): bundle_id = dev.v-key.vtap
vos_read_tid_from_stable_tid_file(): bundle_id_len = 14
vos_read_tid_from_stable_tid_file(): extension_len = 4
vos_read_tid_from_stable_tid_file(): tid_file_name = .dev_v-key_vtap.vos
VOS TRACE: vos_read_tid_from_stable_tid_file(): tid_file read failed
trust_troubleshooting_id_generate(): read tid from file ret = -1370
vos_export_stable_tid_file(): bundle_id = dev.v-key.vtap
vos_export_stable_tid_file(): bundle_id_len = 14
vos_export_stable_tid_file(): extension_len = 4
vos_export_stable_tid_file(): tid_file_name = .dev_v-key_vtap.vos
vos_get_TIDSK(): DFP_Hash[32] =
vos_get_TIDSK(): frk[32] =
vos_get_TIDSK(): tidsk_input_data[38] =
vos_get_TIDSK(): TIDSK[32] =
vos_export_stable_tid_file(): TIDSK[32] =
vos_export_stable_tid_file(): header[5] =
vos_export_stable_tid_file(): signature[32] =
vos_export_stable_tid_file(): buffer[57] =
vos_export_stable_tid_file(): SYSCALL_WRITE_TO_TID_FILE ret = 1
logger_set_tid()
logger_set_tid(): 1d2f5dd15768ec564f45aaa58678e4d4c10adf45
-[VGuardPrivateUtility updateDFPHashInKeyChain] [Line 243] updateDFPHashInKeyChain: new TID: 1D2F5DD15768EC564F45AAA58678E4D4C10ADF45